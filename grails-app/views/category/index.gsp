<!DOCTYPE html>
<html>
    <head>
        <meta name="layout" content="search" />
        <g:set var="entityName" value="${message(code: 'category.label', default: 'Category')}" />
        <title><g:message code="default.list.label" args="[entityName]" /></title>
    </head>
    <body>
    <content tag="nav">
        <form id="searchCategoryForm" class="form-inline my-2 my-lg-0">
            <g:if test="${params.query}">
                <button class="btn btn-outline-warning mr-1 my-2 my-sm-0" type="button" onclick="window.location.href='/category'">Show all</button>
            </g:if>
            <input class="form-control mr-sm-2"
                   type="search"
                   name="query"
                   value="${params.query}"
                   placeholder="Search"
                   aria-label="Search"
            >
            <button class="btn btn-outline-light my-2 my-sm-0" type="submit">Search</button>
        </form>
    </content>
        <a href="#list-category" class="skip" tabindex="-1"><g:message code="default.link.skip.label" default="Skip to content&hellip;"/></a>
        <div class="nav" role="navigation">
            <ul>
                <li><a class="home" href="${createLink(uri: '/')}"><g:message code="default.home.label"/></a></li>
                <li><g:link class="create" action="create"><g:message code="default.new.label" args="[entityName]" /></g:link></li>
                <li><a href="javascript:"
                       data-title="Are you sure?"
                       data-text="Send all unmapped categories"
                       data-icon="warning"
                       data-reload="true"
                       onclick="performAction(this, 'category', 'sendUnmappedCategories')">Send All Unmapped</a>
                </li>
                <li><a href="javascript:"
                       data-title="Are you sure?"
                       data-text="Map all unmapped categories"
                       data-icon="warning"
                       data-reload="true"
                       onclick="performAction(this, 'category', 'mapUnmappedCategories')">Map All Unmapped</a>
                </li>
                <li><a href="javascript:"
                       data-title="Are you sure?"
                       data-text="Delete all unmapped categories from WooCommerce"
                       data-icon="warning"
                       data-reload="true"
                       onclick="performAction(this, 'category', 'deleteUnmappedCategories')">Delete All Unmapped</a>
                </li>
                <li><a href="javascript:"
                       data-title="Are you sure?"
                       data-text="Delete all mapped categories from WooCommerce"
                       data-icon="warning"
                       data-reload="true"
                       onclick="performAction(this, 'category', 'deleteAllMappedCategories')">Delete All Mapped</a>
                </li>
                <li><a href="javascript:"
                       data-title="Are you sure?"
                       data-text="Sync all categories"
                       data-icon="warning"
                       data-reload="true"
                       onclick="performAction(this, 'category', 'syncAll')">Sync All</a>
                </li>
                <li><a href="javascript:"
                       data-title="Are you sure?"
                       data-text="Check all Sales Categories for updates"
                       data-icon="warning"
                       data-reload="true"
                       onclick="performAction(this, 'category', 'updateAllSalesCategories')">Update All Sales Categories</a>
                </li>
            </ul>
        </div>
        <div id="list-category" class="content scaffold-list" role="main">
            <h1><g:message code="default.list.label" args="[entityName]" /></h1>
            <g:if test="${flash.message}">
                <div class="message" role="status">${flash.message}</div>
            </g:if>
            <f:table collection="${categoryList}" />

            <div class="pagination">
                <g:paginate total="${categoryCount ?: 0}" />
            </div>
        </div>
    </body>
</html>