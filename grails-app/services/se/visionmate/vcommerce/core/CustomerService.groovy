package se.visionmate.vcommerce.core

import grails.gorm.transactions.Transactional
import grails.util.Environment
import org.hibernate.Session
import se.visionmate.vcommerce.api.VismaConnectService
import se.visionmate.vcommerce.api.WooConnectService
import se.visionmate.vcommerce.enums.AddressType
import se.visionmate.vcommerce.enums.BatchUpdateType
import se.visionmate.vcommerce.enums.ContactType
import se.visionmate.vcommerce.enums.CustomerRole
import se.visionmate.vcommerce.enums.CustomerType
import se.visionmate.vcommerce.enums.PacketOption
import se.visionmate.vcommerce.enums.PriceClassType
import se.visionmate.vcommerce.visma.Tenant
import se.visionmate.vcommerce.visma.TenantService

class CustomerService {

    static Boolean vismaCustomersPaging

    AppService appService
    OptionService optionService
    PacketService packetService
    VismaConnectService vismaConnectService
    WooConnectService wooConnectService
    AddressService addressService
    ContactService contactService
    PriceService priceService
    UpdateService updateService
    TenantService tenantService

    void initialize() {
        Map<String, String> options = optionService.vismaOptions
        vismaCustomersPaging = Boolean.parseBoolean(options?.visma_customers_paging)
    }

    CustomerType getCustomerType() {
        packetService.isActivePacketOption(PacketOption.CONTACTS) ?
                CustomerType.CONTACT :
                CustomerType.CUSTOMER
    }

    void populateCustomersFromTenants() {
        List<Tenant> tenants = tenantService.getAllNonMainTenants()
        tenants.each { Tenant tenant ->
            tenantService.activateTenant(tenant)
            populateCustomers()
        }
        tenantService.activateMainTenant()
    }

    void populateCustomers(Integer pageNumber = 1, Integer pageSize = 500) {
        if (!vismaCustomersPaging) {
            pageSize = 0
        }
        Map data = [:]
        Customer customer
        List customers = vismaConnectService.getActiveCustomers([pageNumber:pageNumber, pageSize:pageSize]) as List
        if (!customers) {
            println "No customers were found in Visma"
            return
        }

        List customerContacts = []
        List customerLocations = []

        customers.each { customerObj ->
            data.customerNumber = customerObj.number
            data.customerName = customerObj.name
            data.customerClassId = customerObj.customerClass?.id
            data.priceClassId = customerObj.priceClass?.id
            data.currency = customerObj.currencyId
            data.vismaId = customerObj.internalId

            customer = Customer.findByTypeAndNumber(CustomerType.CUSTOMER, data.customerNumber as String)
                    ?: createNewCustomer(data, CustomerType.CUSTOMER)

            if (customer) {
                addDataToCustomer(customer, customerObj)

                // Adding customer locations if the option is active
                if (packetService.isActivePacketOption(PacketOption.LOCATIONS)) {
                    customerLocations = vismaConnectService.getCustomerLocations(customer.number) as List
                    if (customerLocations) {
                        populateCustomerLocations(customerLocations, customer)
                    }
                }

                // Adding customer contacts if the option is active
                if (packetService.isActivePacketOption(PacketOption.CONTACTS)) {
                    customerContacts = vismaConnectService.getCustomerContacts(customer.number) as List
                    if (customerContacts) {
                        populateCustomerContacts(customerContacts, customer)
                    }
                }
            }
        }
        Customer.withSession { Session session ->
            session.flush()
            session.clear()
        }
        if (pageSize > 0 && customers.size() == pageSize) {
            customers = null
            System.gc()
            populateCustomers(++pageNumber)
        }
    }

    /**
     * Add a new customer by "Customer" object from Visma
     * @param customerVismaObj as Object
     * @return customer as Customer
     */
    Customer addNewCustomer(def customerObj) {
        Map data = [:]
        data.customerNumber = customerObj.number
        data.customerName = customerObj.name
        data.customerClassId = customerObj.customerClass?.id
        data.priceClassId = customerObj.priceClass?.id
        data.currency = customerObj.currencyId
        data.vismaId = customerObj.internalId

        Customer customer = Customer.findByTypeAndNumber(CustomerType.CUSTOMER, data.customerNumber as String)
                ?: createNewCustomer(data, CustomerType.CUSTOMER)

        if (!customer) { return null }
        addDataToCustomer(customer, customerObj)

        // Adding customer locations if the option is active
        if (packetService.isActivePacketOption(PacketOption.LOCATIONS)) {
            List customerLocations = vismaConnectService.getCustomerLocations(customer.number) as List
            if (customerLocations) {
                populateCustomerLocations(customerLocations, customer)
            }
        }

        // Adding customer contacts if the option is active
        if (packetService.isActivePacketOption(PacketOption.CONTACTS)) {
            List customerContacts = vismaConnectService.getCustomerContacts(customer.number) as List
            if (customerContacts) {
                populateCustomerContacts(customerContacts, customer)
            }
        }

//        Customer.withSession { Session session ->
//            session.flush()
//        }

        return customer
    }

    /**
     * Creating a new customer
     * @param data as Map
     * @param type as CustomerType
     * @param parentCustomers as Customer(optional) for contacts
     * @param flush as Bool(optional)
     * @return customer as Customer
     */
    @Transactional
    Customer createNewCustomer(Map data, CustomerType customerType, Customer parentCustomer = null, Boolean flush = false) {
        Customer customer = new Customer(
                number: data.customerNumber,
                name: data.customerName,
                tenantId: customerType == CustomerType.CUSTOMER ? tenantService.currentTenantId : null,
                currency: data.currency,
                vismaId: data.vismaId as Long,
                type: customerType,
                role: getCustomerRole(data.customerRole as String),
                customer: parentCustomer,
                active: true
        )
        if (customer.validate()) {
            if (customer.save(flush: flush)) {
                println "[$appService.timeStamp] Customer '$customer' - has been created"
                return customer
            } else {
                println "An error occurred while creating new customer '$data.customerNumber - $data.customerName'"
                if (appService.debugMode) {
                    customer.errors.allErrors.each { println it }
                }
                return null
            }
        } else {
            println "Validation issue occurred while creating new customer '$data.customerNumber - $data.customerName'"
            if (appService.debugMode) {
                customer.errors.allErrors.each { println it }
            }
            return null
        }
    }

    /**
     * Creating a new customer class
     * @param customerClassObj as Object
     * @param customer as Customer
     * @param flush as Bool(optional)
     * @return customerClass as CustomerClass
     */
    @Transactional
    CustomerClass createNewCustomerClass(def customerClassObj, Boolean flush = false) {
        CustomerClass customerClass = new CustomerClass(
                vismaId: customerClassObj.id ?: '',
                description: customerClassObj.description ?: ''
        )
        if (customerClass.validate()) {
            if (customerClass.save(flush: flush)) {
                println "[$appService.timeStamp] Customer class '$customerClass' - has been created"
                return customerClass
            } else {
                println "An error occurred while creating new customerClass '$customerClassObj.id - $customerClassObj.description'"
                if (appService.debugMode) {
                    customerClass.errors.allErrors.each { println it }
                }
                return null
            }
        } else {
            println "Validation issue occurred while creating new customerClass '$customerClassObj.id - $customerClassObj.description'"
            if (appService.debugMode) {
                customerClass.errors.allErrors.each { println it }
            }
            return null
        }
    }

    /**
     * Sending customers to WooCommerce
     * @param customers as List (optional)
     * @param toUpdate as Boolean (optional)
     */
    void sendCustomersToWoo(List<Customer> customers = [], Boolean toUpdate = false) {
        if (!customers) {
            println '\r\nSending unmapped customers to the WooCommerce'
            customers = Customer.withCriteria {
                isNull('wooId')
                eq('type', customerType)
            }.findAll { Customer customer ->
                customer.contacts.any { it.email }
            }
            if (!customers) {
                println 'No unmapped customers found'
                return
            }
            println "${customers.size()} unmapped customers have been found"
        }

        customers.each { Customer customer ->
            if (toUpdate) {
                println "Trying to update the customer '${customer}' in Woo"
                updateCustomerInWoo(customer)
            } else {
                println "Trying to send the customer '${customer}' to Woo"
                sendCustomerToWoo(customer)
            }
        }
    }

    /**
     * Checking for customers updates
     * @param lastModifiedDateTime as String
     * @param pageNumber as Integer
     * @param pageSize as Integer
     */
    @Transactional
    void checkForUpdates(String lastModifiedDateTime, Integer pageNumber = 1, Integer pageSize = 500) {
        if (!vismaCustomersPaging) {
            pageSize = 0
        }
        Map params = [:]
        if (lastModifiedDateTime) {
            params.lastModifiedDateTime = lastModifiedDateTime
            params.lastModifiedDateTimeCondition = '>'
        } else {
            params.pageNumber = pageNumber
            params.pageSize = pageSize
        }

        List customersToCheck = vismaConnectService.getActiveCustomers(params) as List
        if (!customersToCheck) {
            println 'There are no customers to check'
            return
        }

        List newVismaCustomers = []
        List<Customer> customersToUpdate = []
        Customer customer
//        List<Customer> customers = Customer.list()

        customersToCheck.each { customerToCheck ->
            if (customerToCheck.number) {
//                customer = customers.find {
//                    it.number == customerToCheck.number as String
//                }
                customer = Customer.findByNumber(customerToCheck.number as String)

                if (!customer) {
                    newVismaCustomers.push(customerToCheck)
                } else {
                    if (packetService.isActivePacketOption(PacketOption.CONTACTS)) {
                        checkForCustomerContactsUpdates(customer, lastModifiedDateTime)
                    }
                    if (customerUpdated(customer, customerToCheck)) {
                        if (packetService.isActivePacketOption(PacketOption.LOCATIONS)) {
                            customerLocationsUpdated(customer)
                        }
                        customersToUpdate.push(customer)
                    } else if (packetService.isActivePacketOption(PacketOption.LOCATIONS) && customerLocationsUpdated(customer)) {
                        customersToUpdate.push(customer)
                    } else {
                        println "customer data for \"$customer\" is relevant"
                    }
                }
            }
        }

        if (newVismaCustomers) {
            List<Customer> newCustomers = []
            newVismaCustomers.each { newCustomerVismaObj ->
                customer = addNewCustomer(newCustomerVismaObj)
                if (customer) {
                    newCustomers.push(customer)
                }
            }
            if (newCustomers) {
                sendCustomersToWoo(newCustomers)
            }
        }

        if (customersToUpdate) {
            sendCustomersToWoo(customersToUpdate, true)
        }

        if (pageSize > 0 && customersToCheck.size() == pageSize) {
            params = null
            customersToCheck = null
            newVismaCustomers = null
            customersToUpdate = null
//            customers = null
            System.gc()
            checkForUpdates(lastModifiedDateTime, ++pageNumber)
        }
    }

    /**
     * Check a customer for updates
     * @param customer as Customer
     * @param customerVismaObj as Object
     * @return Boolean
     */
    @Transactional
    Boolean customerUpdated(Customer customer, def customerVismaObj) {
        Boolean updated = false
        try {
            Customer.withSession { Session customerSession ->
                // Checking customer fields
                updated = updateService.fieldUpdated(customer, 'name', customerVismaObj.name) || updated
                updated = updateService.fieldUpdated(customer, 'currency', customerVismaObj.currencyId) || updated

                if (customerVismaObj.priceClass) {
                    try {
                        PriceClass priceClass = PriceClass.findByVismaId(customerVismaObj.priceClass.id as String) ?:
                                priceService.createNewPriceClass(PriceClassType.CUSTOMER, customerVismaObj.priceClass)
                        if (customer.priceClass != priceClass) {
                            customer.priceClass = priceClass
                            updated = true
                        }
                    } catch(Exception ex) {
                        println ex.message
                    }
                }

                if (customerVismaObj.customerClass) {
                    try {
                        CustomerClass customerClass = CustomerClass.findByVismaId(customerVismaObj.customerClass.id as String) ?:
                                createNewCustomerClass(customerVismaObj.customerClass)
                        if (customer.customerClass != customerClass) {
                            customer.customerClass = customerClass
                            updated = true
                        }
                    } catch(Exception ex) {
                        println ex.message
                    }
                }

                if (customerVismaObj.mainContact) {
                    Contact mainContact = customer.contacts.find { it.type == ContactType.MAIN_CONTACT }
                    if (!mainContact) {
                        contactService.checkForNewContact(ContactType.MAIN_CONTACT, customer, customerVismaObj.mainContact)
                        updated = true
                    } else {
                        updated = contactService.contactUpdated(mainContact, customerVismaObj.mainContact) || updated
                    }
                }

                if (customerVismaObj.mainAddress) {
                    Address mainAddress = customer.addresses.find { it.type == AddressType.MAIN_ADDRESS }
                    if (!mainAddress) {
                        addressService.checkForNewAddress(AddressType.MAIN_ADDRESS, customer, customerVismaObj.mainAddress)
                        updated = true
                    } else {
                        updated = addressService.addressUpdated(mainAddress, customerVismaObj.mainAddress) || updated
                    }
                }

                if (customerVismaObj.invoiceContact) {
                    Contact invoiceContact = customer.contacts.find { it.type == ContactType.INVOICE_CONTACT }
                    if (!invoiceContact) {
                        contactService.checkForNewContact(ContactType.INVOICE_CONTACT, customer, customerVismaObj.invoiceContact)
                        updated = true
                    } else {
                        updated = contactService.contactUpdated(invoiceContact, customerVismaObj.invoiceContact) || updated
                    }
                }

                if (customerVismaObj.invoiceAddress) {
                    Address invoiceAddress = customer.addresses.find { it.type == AddressType.INVOICE_ADDRESS }
                    if (!invoiceAddress) {
                        addressService.checkForNewAddress(AddressType.INVOICE_ADDRESS, customer, customerVismaObj.invoiceAddress)
                        updated = true
                    } else {
                        updated = addressService.addressUpdated(invoiceAddress, customerVismaObj.invoiceAddress) || updated
                    }
                }

                if (customerVismaObj.deliveryContact) {
                    Contact deliveryContact = customer.contacts.find { it.type == ContactType.DELIVERY_CONTACT }
                    if (!deliveryContact) {
                        contactService.checkForNewContact(ContactType.DELIVERY_CONTACT, customer, customerVismaObj.deliveryContact)
                        updated = true
                    } else {
                        updated = contactService.contactUpdated(deliveryContact, customerVismaObj.deliveryContact) || updated
                    }
                }

                if (customerVismaObj.deliveryAddress) {
                    Address deliveryAddress = customer.addresses.find { it.type == AddressType.DELIVERY_ADDRESS }
                    if (!deliveryAddress) {
                        addressService.checkForNewAddress(AddressType.DELIVERY_ADDRESS, customer, customerVismaObj.deliveryAddress)
                        updated = true
                    } else {
                        updated = addressService.addressUpdated(deliveryAddress, customerVismaObj.deliveryAddress) || updated
                    }
                }
                customerSession.flush()
            }
        } catch(Exception ex) {
            println ex.message
            if (appService.debugMode) {
                ex.printStackTrace()
            }
        }
        updated
    }

    /**
     * Checking for customer contacts updates
     * customerContactsUpdated
     * @param customer as Customer
     * @param lastModifiedDateTime as String
     */
    @Transactional
    void checkForCustomerContactsUpdates(Customer customer, String lastModifiedDateTime) {
        Map params = [:]
        if (lastModifiedDateTime) {
            params.lastModifiedDateTime = lastModifiedDateTime
            params.lastModifiedDateTimeCondition = '>'
        }
        List newCustomerContacts = []
        List<Customer> customerContactsToDelete = []
        List<Customer> customerContactsToUpdate = []
        List customerContactsToCheck = vismaConnectService.getCustomerContacts(customer.number, params) as List
        if (!customerContactsToCheck && customer.customerContacts && !lastModifiedDateTime) {
            customer.customerContacts.each { customerContactsToDelete.push(it) }
        } else if (!customerContactsToCheck) {
            return
        } else if (customerContactsToCheck.size() != customer.customerContacts.size() && !lastModifiedDateTime) {
            customer.customerContacts.each { customerContact ->
                if (!customerContactsToCheck.find { it.contactId == customerContact.vismaId }) {
                    customerContactsToDelete.push(customerContact)
                }
            }
        } else if (customerContactsToCheck.any { it.active == false }) {
            customer.customerContacts.each { customerContact ->
                if (customerContactsToCheck.find { it.active == false && it.contactId == customerContact.vismaId }) {
                    customerContactsToDelete.push(customerContact)
                }
            }
        }

        Customer customerContact
        customerContactsToCheck.each { customerContactToCheck ->
            customerContact = Customer.findByTypeAndVismaId(CustomerType.CONTACT, customerContactToCheck.contactId as Long)
            if (!customerContact) {
                newCustomerContacts.push(customerContactToCheck)
            } else if (customerContactUpdated(customerContact, customerContactToCheck)) {
                customerContactsToUpdate.push(customerContact)
            }
        }

        if (newCustomerContacts) {
            populateCustomerContacts(newCustomerContacts, customer, true)
        }
        if (customerContactsToUpdate) {
            customerContactsToUpdate.collate(100).each {
                wooConnectService.batchUpdate(BatchUpdateType.CUSTOMER, null, it, null)
            }
        }
        if (customerContactsToDelete) {
            def result
            Customer toDelete
            customerContactsToDelete.collate(100).each {
                result = wooConnectService.batchUpdate(BatchUpdateType.CUSTOMER, null, null, it.collect { it.wooId })
                if (result?.delete) {
                    result.delete.each { customerWooObj ->
                        Customer.withNewTransaction {
                            toDelete = Customer.findByWooId(customerWooObj.id as Long)
                            try {
                                toDelete?.delete(flush: true)
                            } catch (Exception ex) {
                                println "[${new Date().format('yyyy-MM-dd HH:mm:ss')}] EXCEPTION: ${ex.message}"
                            }
                        }
                    }
                } else if (!result?.delete && customerContactsToDelete.any { it.wooId == null }) {
                    customerContactsToDelete.each { Customer customerContactToDelete ->
                        if (!customerContactToDelete.wooId) {
                            Customer.withNewTransaction {
                                try {
                                    Customer.get(customerContactToDelete.id)?.delete(flush: true)
                                } catch (Exception ex) {
                                    println "[${new Date().format('yyyy-MM-dd HH:mm:ss')}] EXCEPTION: ${ex.message}"
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * Checking for customer contacts updates
     * Specifically for regular updates
     * @param lastModifiedDateTime as String
     */
    @Transactional
    void checkForCustomerContactsUpdates(String lastModifiedDateTime) {
        if (!lastModifiedDateTime) return

        Map<String, ?> params = [
                lastModifiedDateTime: lastModifiedDateTime,
                lastModifiedDateTimeCondition: '>'
        ]

        List customerContactsToCheck = vismaConnectService.getCustomerContacts(params) as List
        if (!customerContactsToCheck) {
            println '...no customer contacts to check'
            return
        }

        List newCustomerContacts = []
        List<Customer> customerContactsToUpdate = []
        Customer customerContact
        customerContactsToCheck.each { customerContactToCheck ->
            customerContact = Customer.findByTypeAndVismaId(CustomerType.CONTACT, customerContactToCheck.contactId as Long)
            if (!customerContact) {
                newCustomerContacts.push(customerContactToCheck)
            } else if (customerContactUpdated(customerContact, customerContactToCheck)) {
                customerContactsToUpdate.push(customerContact)
            }
        }

        if (newCustomerContacts) {
            populateCustomerContacts(newCustomerContacts, true)
        }
        if (customerContactsToUpdate) {
            customerContactsToUpdate.collate(100).each {
                wooConnectService.batchUpdate(BatchUpdateType.CUSTOMER, null, it, null)
            }
        }
    }

    /**
     * Checking for the customer contact updates
     * @param customerContact as Customer
     * @param customerContactVismaObj as Object
     * @return bool
     */
    @Transactional
    Boolean customerContactUpdated(Customer customerContact, def customerContactVismaObj) {
        Boolean updated = false
        Contact contact = customerContact.contacts.find { it.type == ContactType.MAIN_CONTACT }
        Address address  = customerContact.addresses.find { it.type == AddressType.MAIN_ADDRESS }
        Customer.withSession { Session session ->
            updated = updateService.fieldUpdated(customerContact, 'name', customerContactVismaObj.displayName) || updated
            updated = updateService.fieldUpdated(customerContact, 'active', customerContactVismaObj.active) || updated
            updated = customerRoleUpdated(customerContact, customerContactVismaObj.position as String) || updated
            if (contact) {
                updated = contactService.contactUpdated(contact, customerContactVismaObj) || updated
            }
            if (address) {
                updated = addressService.addressUpdated(address, customerContactVismaObj.address) || updated
            }
            if (updated) session.flush()
        }
        updated
    }

    @Transactional
    void checkForCustomerLocationsUpdates(String lastModifiedDateTime) {
        if (!lastModifiedDateTime) return

        Map<String, ?> params = [
                lastModifiedDateTime: lastModifiedDateTime,
                lastModifiedDateTimeCondition: '>'
        ]

        List customerLocationsToCheck = vismaConnectService.getCustomerLocations(params) as List
        if (!customerLocationsToCheck) {
            println '...no customer locations to check'
            return
        }

        Customer customer
        Location location
        Set<Customer> customerContactsToUpdate = []
        customerLocationsToCheck.each { locationToCheck ->
            customer = Customer.findByNumber(locationToCheck.baccount?.number as String)
            if (!customer) return
            location = Location.findByCustomerAndVismaId(customer, locationToCheck.locationId as String)
            if (!location && addNewCustomerLocation(locationToCheck, customer)) {
                customer.customerContacts.each { Customer customerContact ->
                    customerContactsToUpdate.add(customerContact)
                }
            } else if (customerLocationUpdated(location, locationToCheck)) {
                customer.customerContacts.each { Customer customerContact ->
                    customerContactsToUpdate.add(customerContact)
                }
            }
        }

        if (customerContactsToUpdate) {
            customerContactsToUpdate.collate(100).each {
                wooConnectService.batchUpdate(BatchUpdateType.CUSTOMER, null, it, null)
            }
        }
    }

    /**
     * Checking for customer locations updates
     * @param customer as Customer
     * @param lastModifiedDateTime as String
     * @return bool
     */
    @Transactional
    Boolean customerLocationsUpdated(Customer customer) {
        Map params = [:]
        List newCustomerLocations = []
        List<Location> customerLocationsToDelete = []
        List customerLocationsToCheck = vismaConnectService.getCustomerLocations(customer.number, params) as List
        if (!customerLocationsToCheck && customer.locations) {
            customer.locations.each { customerLocationsToDelete.push(it) }
        } else if (customerLocationsToCheck.size() != customer.locations.size()) {
            customer.locations.each { customerLocation ->
                if (!customerLocationsToCheck.find { it.locationId == customerLocation.vismaId }) {
                    customerLocationsToDelete.push(customerLocation)
                }
            }
        } else if (!customerLocationsToCheck) {
            return false
        }

        Boolean updated = false
        Location customerLocation
        customerLocationsToCheck.each { customerLocationToCheck ->
            customerLocation = Location.findByCustomerAndVismaId(customer, customerLocationToCheck.locationId as String)
            if (!customerLocation) {
                newCustomerLocations.push(customerLocationToCheck)
            } else if (customerLocationUpdated(customerLocation, customerLocationToCheck)) {
                updated = true
            }
        }

        if (newCustomerLocations) {
            populateCustomerLocations(newCustomerLocations, customer)
            updated = true
        }
        if (customerLocationsToDelete) {
            customerLocationsToDelete.each { Location location ->
                Location.withNewSession { Session session ->
                    try {
                        location.delete(flush: true)
                        session.flush()
                        session.clear()
                    } catch (Exception ex) {
                        println "[$appService.timeStamp] EXCEPTION: ${ex.message}"
                    }
                }
            }
            updated = true
        }
        updated
    }

    /**
     * Checking for the customer location updates
     * @param customerLocation as Location
     * @param customerLocationVismaObj as Object
     * @return bool
     */
    Boolean customerLocationUpdated(Location customerLocation, def customerLocationVismaObj) {
        Boolean updated = false
        Location.withSession {Session session ->
            updated = updateService.fieldUpdated(customerLocation, 'name', customerLocationVismaObj.locationName) || updated
            updated = updateService.fieldUpdated(customerLocation, 'active', customerLocationVismaObj.active) || updated
            updated = contactService.contactUpdated(customerLocation.contact, customerLocationVismaObj.contact) || updated
            updated = addressService.addressUpdated(customerLocation.address, customerLocationVismaObj.address) || updated
            if (updated) session.flush()
        }
        updated
    }

    /**
     * Sending customer to WooCommerce
     * @param customer as Customer
     */
    void sendCustomerToWoo(Customer customer) {
        Map data = wooConnectService.mapCustomerToWooApi(customer)
        println "Sending customer '${customer}' to the WooCommerce"
        Object wooCustomerObj = wooConnectService.sendCustomerToWoo(data)
        if (!wooCustomerObj) { return }
        println "Customer '$customer' have been sent to the WooCommerce"
        try {
            customer.wooId = wooCustomerObj.id ?: null
            customer.save(flush: true)
        } catch(Exception ex) {
            println ex.message
            if (appService.debugMode) {
                ex.printStackTrace()
            }
        }
    }

    /**
     * Updating customer in the WooCommerce
     * @param customer as Customer
     */
    void updateCustomerInWoo(Customer customer) {
        if(!customer.wooId) {
            println "No WooId! '${customer}' can't be updated"
            return
        }

        Map<String, ?> data = wooConnectService.mapCustomerToWooApi(customer, true)
        println "Updating customer '${customer}' in the WooCommerce"
        Object wooCustomerObj = wooConnectService.updateCustomerInWoo(data, customer.wooId)
        if (!wooCustomerObj) { return }
        println "Customer '$customer' have been updated in the WooCommerce"
    }

    /**
     * Sync all customers(customerContacts) to Woo
     * @param batchSize: Integer
     */
    void syncAllCustomersToWoo(Integer batchSize = 100) {
        println "Trying to sync customers..."
        Set<Customer> customers = Customer.withCriteria {
            isNotNull('wooId')
            eq('type', CustomerType.CONTACT)
        }.findAll { Customer customer ->
            customer.contacts.any { it.email }
        }
        if (!customers) {
            println "No customers to sync with Woo"
        }

        Integer total = customers.size()
        Integer current = 0
        Object updatedCustomers
        println "...found $total customers to sync..."
        customers.collate(batchSize).each { List<Customer> customerBatch ->
            updatedCustomers = wooConnectService.batchUpdate(BatchUpdateType.CUSTOMER, null, customerBatch, null)
            println "...customers synced ${current += updatedCustomers?.update?.size()} / $total"
        }
    }

    /**
     * Adding data to customer
     * @param customer as Customer
     * @param customerObj as Object
     */
    void addDataToCustomer(Customer customer, def customerObj) {
        if (customerObj.customerClass) {
            CustomerClass customerClass = CustomerClass.findByVismaId(customerObj.customerClass.id as String) ?:
                    createNewCustomerClass(customerObj.customerClass)
            customerClass.addToCustomers(customer).save()
        }
        if (customerObj.priceClass) {
            PriceClass priceClass = PriceClass.findByVismaId(customerObj.priceClass.id as String) ?:
                    priceService.createNewPriceClass(PriceClassType.CUSTOMER, customerObj.priceClass)
            priceClass.addToCustomers(customer).save()
        }
        if (customerObj.mainAddress) {
            addressService.checkForNewAddress(AddressType.MAIN_ADDRESS, customer, customerObj.mainAddress)
        }
        if (customerObj.mainContact) {
            contactService.checkForNewContact(ContactType.MAIN_CONTACT, customer, customerObj.mainContact)
        }
        if (customerObj.invoiceAddress) {
            addressService.checkForNewAddress(AddressType.INVOICE_ADDRESS, customer, customerObj.invoiceAddress)
        }
        if (customerObj.invoiceContact) {
            contactService.checkForNewContact(ContactType.INVOICE_CONTACT, customer, customerObj.invoiceContact)
        }
        if (customerObj.deliveryAddress) {
            addressService.checkForNewAddress(AddressType.DELIVERY_ADDRESS, customer, customerObj.deliveryAddress)
        }
        if (customerObj.deliveryContact) {
            contactService.checkForNewContact(ContactType.DELIVERY_CONTACT, customer, customerObj.deliveryContact)
        }
    }

    /**
     * Populating customer contacts
     * @param newCustomerContacts as List (optional)
     * @param sendToWoo as Boolean (optional)
     */
    void populateCustomerContacts(List newCustomerContacts = [], Boolean sendToWoo = false) {

        List customerContacts = newCustomerContacts ?: vismaConnectService.getCustomerContacts() as List
        if (!newCustomerContacts && !customerContacts) {
            println "...no customer contacts were found in Visma"
            return
        }

        Map data = [:]
        Customer customer, customerContact
        getActiveCustomerContacts(customerContacts).each { contactObj ->
            customer = Customer.findByTypeAndNumber(CustomerType.CUSTOMER, contactObj.businessAccount as String)
            if (!customer) { return }

            data.customerName = contactObj.displayName ?: "${contactObj.lastName}, ${contactObj.firstName}"
            data.currency = customer.currency
            data.vismaId = contactObj.contactId as Long

            customerContact = Customer.findByTypeAndVismaId(CustomerType.CONTACT, data.vismaId as Long)
                    ?: createNewCustomer(data, CustomerType.CONTACT, customer) // creating a new customer contact

            if (customerContact) {
                addDataToCustomerContact(customerContact, contactObj)
                if (sendToWoo) {
                    sendCustomerToWoo(customerContact)
                }
            }
        }
    }

    /**
     * Populating customer contacts of a specific customer
     * @param customerContacts as List
     * @param customer as Customer
     * @param sendToWoo as Boolean(optional)
     */
    void populateCustomerContacts(List customerContacts, Customer customer, Boolean sendToWoo = false) {
        Map data = [:]
        Customer customerContact
        customerContacts.each { contactObj ->
            data.customerName = contactObj.displayName ?: "${contactObj.lastName}, ${contactObj.firstName}"
            data.customerRole = contactObj.position
            data.currency = customer.currency
            data.vismaId = contactObj.contactId as Long

            customerContact = Customer.findByTypeAndVismaId(CustomerType.CONTACT, data.vismaId as Long)
                    ?: createNewCustomer(data, CustomerType.CONTACT, customer) // creating a new customer contact

            if (customerContact) {
                addDataToCustomerContact(customerContact, contactObj)
                if (sendToWoo) {
                    Customer.withSession { Session session ->
                        session.flush()
                        sendCustomerToWoo(customerContact.refresh())
                    }
                }
            }
        }
    }

    /**
     * Adding data to customer contact
     * @param contact as Customer
     * @param contactObj as Object
     */
    void addDataToCustomerContact(Customer contact, def contactObj) {
        addressService.checkForNewAddress(AddressType.MAIN_ADDRESS, contact, contactObj.address)
        contactService.checkForNewContact(ContactType.MAIN_CONTACT, contact, contactObj)
    }

    /**
     * Get a range of only 'Active' customers
     * @param customers as List of Visma customer objects
     * @return
     */
    List getActiveCustomers(List customers) {
        if (customers.every { !it.status }) { return null }
        return customers.findAll {it.status == 'Active'}
    }

    /**
     * Get a range of only 'Active' customer contacts
     * @param contacts as List of Visma contact objects
     * @return
     */
    List getActiveCustomerContacts(List contacts) {
        if (contacts.every { !it.active }) { return null }
        return contacts.findAll { it.active == Boolean.TRUE }
    }

    /**
     * Populating customer locations
     * @param pageNumber as Integer
     * @param pageSize as Integer
     */
    void populateCustomerLocations(Integer pageNumber = 1, Integer pageSize = 500) {
        Map data = [:]
        Customer customer
        Location location

        List customerLocations = vismaConnectService.getCustomerLocations([pageNumber:pageNumber, pageSize:pageSize]) as List
        if (!customerLocations) {
            println "No customer locations were found in Visma"
            return
        }

        customerLocations.each { locationObj ->
            customer = Customer.findByTypeAndNumber(CustomerType.CUSTOMER, locationObj.baccount?.number as String)
            if (!customer) { return }

            data.name = locationObj.locationName
            data.vismaId = locationObj.locationId
            data.active = locationObj.active as Boolean

            location = Location.findByCustomerAndVismaId(customer, data.vismaId as String)
                    ?: createNewLocation(data, customer)

            if (location) {
                addDataToLocation(location, locationObj)
            }
        }
        if (customerLocations.size() > 0) {
            customerLocations = []
            System.gc()
            populateCustomerLocations(++pageNumber)
        }
    }

    /**
     * Populating customer locations of a specific customer
     * @param customerLocations as List
     * @param customer as Customer
     */
    void populateCustomerLocations(List customerLocations, Customer customer) {
        Map data = [:]
        Location location
        customerLocations.each { locationObj ->
            data.name = locationObj.locationName
            data.vismaId = locationObj.locationId
            data.active = locationObj.active as Boolean

            location = Location.findByCustomerAndVismaId(customer, data.vismaId as String)
                    ?: createNewLocation(data, customer)

            if (location) {
                addDataToLocation(location, locationObj)
            }
        }
    }

    Boolean addNewCustomerLocation(Object locationVismaObj, Customer customer) {
        Map<String, ?> data = [
                name: locationVismaObj.locationName,
                vismaId: locationVismaObj.locationId,
                active: locationVismaObj.active as Boolean
        ]
        try {
            Location location = Location.findByCustomerAndVismaId(customer, data.vismaId as String)
                    ?: createNewLocation(data, customer)

            if (location) {
                addDataToLocation(location, locationVismaObj)
            }
            true
        } catch (Exception ex) {
            println "EXCEPTION | while adding a new customer location: $ex.message"
            if (appService.debugMode) {
                println ex.stackTrace
            }
            false
        }
    }

    /**
     * Create a new location for a customer
     * @param data as Map
     * @param customer as Customer
     * @param flush as Bool(optional)
     * @return
     */
    @Transactional
    Location createNewLocation(Map data, Customer customer, Boolean flush = false) {
        Location location = new Location(
                name: data.name,
                vismaId: data.vismaId,
                active: data.active,
                customer: customer
        )
        if (location.validate()) {
            if (location.save(flush: flush)) {
                println "[$appService.timeStamp] $location - has been created"
                return location
            } else {
                println 'An error occurred while creating a new location'
                if (appService.debugMode) {
                    customer.errors.allErrors.each { println it }
                }
                return null
            }
        } else {
            println 'Validation issue occurred while creating a new location'
            if (appService.debugMode) {
                location.errors.allErrors.each { println it }
            }
            return null
        }
    }

    /**
     * Adding data to location
     * @param location as Location
     * @param locationObj as Object
     */
    void addDataToLocation(Location location, def locationObj) {
        addressService.checkForNewAddress(AddressType.LOCATION, location, locationObj.address)
        contactService.checkForNewContact(ContactType.LOCATION, location, locationObj.contact)
    }

    /**
     * Checking for the customers updates
     * @param customer as Customer
     * @param lastModifiedDateTime as String
     * @return
     */
    @Transactional
    Boolean checkForCustomerUpdates(Customer customer, String lastModifiedDateTime) {
        if (customer.type != CustomerType.CUSTOMER) {
            println "Wrong type of customer"
            return false
        }

        Map params = [:]
        if (lastModifiedDateTime) {
            params.lastModifiedDateTime = lastModifiedDateTime
            params.lastModifiedDateTimeCondition = '>'
        }

        def customerToCheck = vismaConnectService.getCustomer(customer.number, params)
        if (!customerToCheck) {
            println 'There is no customer to check'
            return false
        }

        checkForCustomerContactsUpdates(customer, lastModifiedDateTime)
        if (customerUpdated(customer, customerToCheck)) {
            customerLocationsUpdated(customer)
            return true
        } else if (customerLocationsUpdated(customer)) {
            return true
        } else {
            println "customer data for \"$customer\" is relevant"
            return false
        }
    }

    /**
     * Get a customer role
     * @param role as String
     * @return
     */
    CustomerRole getCustomerRole(String customerRole) {
        switch (customerRole?.toUpperCase()) {
            case 'ADMIN':
                return CustomerRole.ADMIN
            case 'USER':
                return CustomerRole.CUSTOMER
            default:
                return CustomerRole.UNDEFINED
        }
    }

    /**
     * Check if customer role updated
     * @param customer as Customer
     * @param customerRole as String
     * @return bool
     */
    @Transactional
    Boolean customerRoleUpdated(Customer customer, String customerRole) {
        if (customer.role != getCustomerRole(customerRole)) {
            customer.role = getCustomerRole(customerRole)
            return true
        }
        return false
    }

    /**
     * Deleting all companies from WooCommerce
     */
    @Transactional
    String deleteCompaniesFromWoo() {
        List<Customer> customersToDelete = Customer.withCriteria {
            and {
                isNotNull('wooId')
                eq('type', CustomerType.CUSTOMER)
            }
        } as List
        if (!customersToDelete) {
            return 'No companies found to delete'
        }
        def wooCustomersBatchObj
        Customer customer
        customersToDelete.collate(100).each { List<Customer> customersBatch ->
            wooCustomersBatchObj = wooConnectService.batchUpdate(BatchUpdateType.CUSTOMER, null, null, customersBatch.collect { it.wooId })
            wooCustomersBatchObj?.delete?.each { wooCustomerObj ->
                customer = customersToDelete.find { it.wooId == wooCustomerObj.id as Long } as Customer
                if (customer) {
                    customer.wooId = null
                }
            }
            Customer.withSession { Session session ->
                session.flush()
            }
        }
    }

    @Transactional
    void fixCustomerWooId() {
        List<Customer> customersToFix = Customer.findAllByWooId(null)
        if (customersToFix.size() == 0) { return }
        println "...found ${customersToFix.size()} to fix..."

        Object wooCustomer
        Integer fixed = 0
        customersToFix.each { Customer customerToFix ->
            wooCustomer = wooConnectService.findCustomerByEmail(customerToFix.getMainEmail())
            if (wooCustomer) {
                customerToFix.wooId = wooCustomer.id
                customerToFix.save flush: true
                fixed++
            }
        }
        println "...$fixed fixed..."
    }

}
