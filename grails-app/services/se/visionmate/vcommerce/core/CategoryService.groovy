package se.visionmate.vcommerce.core

import com.opencsv.CSVReader
import grails.gorm.transactions.Transactional
import org.hibernate.Session
//import org.hibernate.SessionFactory
import se.visionmate.vcommerce.api.VismaConnectService
import se.visionmate.vcommerce.api.WooConnectService
import se.visionmate.vcommerce.enums.CategorySource
import se.visionmate.vcommerce.enums.CategoryType
import se.visionmate.vcommerce.enums.Language
import se.visionmate.vcommerce.enums.PacketOption
import se.visionmate.vcommerce.exception.CategoryUpdateException
import se.visionmate.vcommerce.visma.Tenant
import se.visionmate.vcommerce.visma.TenantService

class CategoryService {

    List<Category> categoriesToDelete

    static String ITEM_CLASS_ID, ITEM_CLASS_TYPE, ITEM_CLASS_DESCRIPTION
    static Long DEFAULT_CATEGORY_ID

    AppService appService
    OptionService optionService
    VismaConnectService vismaConnectService
    WooConnectService wooConnectService
    UpdateService updateService
    PacketService packetService
    TenantService tenantService

    static final String CATEGORY_ATTRIBUTE_ID = CategoryType.CAT.attributeId
    static final String SUBCATEGORY_ATTRIBUTE_ID = CategoryType.SUBCAT.attributeId
    static final String SUB2CATEGORY_ATTRIBUTE_ID = CategoryType.SUB2CAT.attributeId
    static final int CATEGORY_LENGTH = CategoryType.CAT.length
    static final int SUBCATEGORY_LENGTH = CategoryType.SUBCAT.length
    static final int SUB2CATEGORY_LENGTH = CategoryType.SUB2CAT.length
//    static final String CATEGORY_END = CategoryType.CAT.end
//    static final String SUBCATEGORY_END = CategoryType.SUBCAT.end

    void initialize() {
        Map<String, String> vismaOptions = optionService.vismaOptions
        ITEM_CLASS_ID = vismaOptions.visma_default_item_class_id
        ITEM_CLASS_TYPE = vismaOptions.visma_default_item_class_type
        ITEM_CLASS_DESCRIPTION = vismaOptions.visma_default_item_class_description
        DEFAULT_CATEGORY_ID = optionService.getOptionValue('default_category_id') as Long
    }

    /**
     * Getting category source
     * IMPORTANT! Based on packaging
     * @return CategorySource.SALES_CATEGORY
     */
    CategorySource getCategorySource() {
        packetService.isActivePacketOption(PacketOption.SALES_CATEGORIES) ?
                CategorySource.SALES_CATEGORY :
                CategorySource.ITEM_CLASS
    }

    /**
     * Getting all categories from Visma and putting to the local DB
     * @param categorySource: ENUM (default: SALES_CATEGORY)
     */
    void populateCategories() {
        switch (categorySource) {
            case CategorySource.ITEM_CLASS:
                populateCategoriesFromItemClasses()
                break
            case CategorySource.SALES_CATEGORY:
                populateCategoriesFromSalesCategories(true)
                break
        }
    }

    /**
     * Populates categories from item classes in Visma
     * Retrieves categories, subcategories, and sub2categories and creates them in the database
     */
    void populateCategoriesFromItemClasses() {
        List categories = getCategoriesFromItemClass()
        if (!categories) {
            log.warn('No categories were found in Visma item classes')
            return
        }

        List subcategories = getSubcategoriesFromItemClass()
        List sub2categories = getSub2categoriesFromItemClass()

        Category.withTransaction { status ->
            try {
                categories.each { cat ->
                    Category category = findOrCreateCategory(cat.description, cat.id, null)
                    if (category) {
                        processSubcategories(category, subcategories, sub2categories)
                    }
                }
            } catch (Exception e) {
                status.setRollbackOnly()
                log.error("Failed to populate categories: ${e.message}", e)
                throw e
            }
        }
    }

    /**
     * Processes subcategories and sub2categories for a given main category.
     *
     * @param category: Category    The main category
     * @param subcategories: List<Object> of subcategories
     * @param sub2categories: List<Object> of sub2categories
     */
    private void processSubcategories(Category category, List subcategories, List sub2categories) {
        subcategories.findAll { it.id.take(2) == category.vismaId }.each { subcat ->
            Category subcategory = findOrCreateCategory(subcat.description, subcat.id, category)
            if (subcategory) {
                sub2categories.findAll { it.id.take(4) == subcategory.vismaId }.each { sub2cat ->
                    findOrCreateCategory(sub2cat.description, sub2cat.id, subcategory)
                }
            }
        }
    }

    /**
     * Finds an existing category by Visma ID or creates a new one if it doesn't exist.
     *
     * @param name: String  The name of the category
     * @param vismaId: String   The unique Visma ID of the category
     * @param parent: Category  The parent category, if any (default: null for main categories)
     * @return The found or newly created Category instance
     */
    private Category findOrCreateCategory(String name, String vismaId, Category parent = null) {
        Category category = Category.findByVismaId(vismaId)
        if (!category) {
            category = createNewCategory(name, vismaId, parent)
        }
        return category
    }

    void populateCategoriesFromSalesCategories(Boolean sessionClear = false) {
        List<Object> salesCategories = vismaConnectService.getSalesCategories() as List
        if (!salesCategories) {
            println 'No SalesCategories to populate'
            return
        }

        Category.withSession {Session session ->
            Category category
            salesCategories.each {categoryVismaObj ->
                category = Category.findByVismaId(categoryVismaObj.categoryID as String)
                if (!category) {
                    Map<String, ?> categoryData = [
                            name: categoryVismaObj.description,
                            vismaId: categoryVismaObj.categoryID,
                            sortOrder: categoryVismaObj.sortOrder,
                            type: CategoryType.CAT
                    ]
                    category = createNewCategory(categoryData)
                }
                if (category && categoryVismaObj.subCategories) {
                    List<Map<String, ?>> subcategories = [[
                            parentCategory: category,
                            subcategiries: categoryVismaObj.subCategories
                    ]]
                    populateSubCategoriesFromSalesCategories(subcategories)
                }
            }
            session.flush()
            if (sessionClear) session.clear()
        }
    }

    void populateSubCategoriesFromSalesCategories(List<Map<String, ?>> subcategories = []) {
        if (!subcategories) return
        List<Map<String, ?>> currentSubcategories = []
        Category.withSession {Session session ->
            Category subcategory
            subcategories.each {Map<String, ?> subcatData ->
                subcatData.subcategiries.each {subcategoryVismaObj ->
                    subcategory = Category.findByVismaId(subcategoryVismaObj.categoryID as String)
                    if (!subcategory) {
                        Map<String, ?> subcategoryData = [
                                name: subcategoryVismaObj.description,
                                vismaId: subcategoryVismaObj.categoryID,
                                sortOrder: subcategoryVismaObj.sortOrder,
                                type: CategoryType.SUBCAT
                        ]
                        subcategory = createNewCategory(subcategoryData as Map, subcatData.parentCategory as Category)
                    }
                    if (subcategory && subcategoryVismaObj.subCategories) {
                        currentSubcategories << [
                                parentCategory: subcategory,
                                subcategiries: subcategoryVismaObj.subCategories
                        ]
                    }
                }
            }
            session.flush()
        }
        if (currentSubcategories) {
            populateSubCategoriesFromSalesCategories(currentSubcategories)
        }
    }

    /**
     * Creating a new category/subcategory
     * @param name: String
     * @param vismaId: String
     * @param parentCategory: Category (default: null)
     * @param flush: Boolean (default: false)
     * @return
     */
    @Transactional
    Category createNewCategory(String name, String vismaId, Category parentCategory = null, Boolean flush = false) {
        Category category = new Category(
                name: name,
                vismaId: vismaId,
                type: getType(vismaId),
                parentCategory: parentCategory
        )
        saveNewCategory(category, flush)
    }

    /**
     * Creating a new category/subcategory
     * @param data: Map<Sting, ?>
     * @param parentCategory: Category (default: null)
     * @param flush: Boolean (default: false)
     * @return
     */
    @Transactional
    Category createNewCategory(Map<String, ?> data, Category parentCategory = null, Boolean flush = false) {
        Category category = new Category(
                name: data.name,
                vismaId: data.vismaId,
                sortOrder: data.sortOrder as Integer,
                type: data.type as CategoryType,
                parentCategory: parentCategory
        )
        saveNewCategory(category, flush)
    }

    /**
     * Save a new category/subcategory
     * @param category: Category
     * @param flush: Boolean (default: false)
     * @return
     */
    Category saveNewCategory(Category category, Boolean flush = false) {
        if (category.validate()) {

            // Add multilingual support
            if (appService.isMultilingual) {
                List<Tenant> tenants = tenantService.getAllNonMainTenants()
                tenants.each { Tenant tenant ->
                    Object categoryObj = getCategoryObjFromItemClassByLang(category.vismaId, tenant.lang)
                    if (categoryObj) {
                        category.setName(tenant.lang, categoryObj.description as String)
                        category.setDescription(tenant.lang, categoryObj.description as String)
                    }
                }
            }

            if (category.save(flush: flush)) {
                println "[$appService.timeStamp] $category - has been created"
                return category
            } else {
                log.error("An error occurred while creating a new category - '${category}'")
                if (appService.debugMode) {
                    category.errors.allErrors.each { println it }
                }
                return null
            }
        } else {
            log.error("Validation issue occurred while creating a new category - '${category}'")
            if (appService.debugMode) {
                category.errors.allErrors.each { println it }
            }
            return null
        }
    }

    /**
     * Determines the category type based on the Visma category ID.
     *
     * @param categoryVismaId as String
     * @return CategoryType enum or null if the ID format is unknown
     * @throws IllegalArgumentException
     */
    CategoryType getType(String categoryVismaId) {
        if (categoryVismaId == null) {
            throw new IllegalArgumentException("Category Visma ID cannot be null")
        }

        int idLength = categoryVismaId.length()

        switch (idLength) {
            case CATEGORY_LENGTH:
                return CategoryType.CAT
            case SUBCATEGORY_LENGTH:
                return CategoryType.SUBCAT
            case SUB2CATEGORY_LENGTH:
                return CategoryType.SUB2CAT
            default:
                return null
        }
    }



    /**
     * Gets category details from item classes.
     * @return categories as List of Objects or null if item class not found
     */
    List getCategoriesFromItemClass() {
        return getAttributeDetailsFromItemClass(CATEGORY_ATTRIBUTE_ID)
    }

    /**
     * Retrieves a category object from an item class based on ID and language.
     *
     * @param id The category ID to look up
     * @param lang The language to use for tenant activation
     * @return category as object or null
     */
    def getCategoryObjFromItemClassByLang(String id, Language lang) {
        if (id == null || lang == null) {
            return null
        }

        def categoryObj = null
        try {
            tenantService.activateTenantByLang(lang)

            // Select appropriate method based on ID length
            switch (id.length()) {
                case CATEGORY_LENGTH:
                    categoryObj = getCategoriesFromItemClass().find { it.id == id }
                    break
                case SUBCATEGORY_LENGTH:
                    categoryObj = getSubcategoriesFromItemClass().find { it.id == id }
                    break
                case SUB2CATEGORY_LENGTH:
                    categoryObj = getSub2categoriesFromItemClass().find { it.id == id }
                    break
            }
        } finally {
            tenantService.activateMainTenant()
        }

        return categoryObj
    }

    /**
     * Gets subcategory details from item classes.
     * @return subcategories as List of Objects or null if item class not found
     */
    List getSubcategoriesFromItemClass() {
        return getAttributeDetailsFromItemClass(SUBCATEGORY_ATTRIBUTE_ID)
    }

    /**
     * Gets sub-subcategory details from item classes.
     * @return sub-subcategories as List of Objects or null if item class not found
     */
    List getSub2categoriesFromItemClass() {
        return getAttributeDetailsFromItemClass(SUB2CATEGORY_ATTRIBUTE_ID)
    }

    /**
     * Helper method to get attribute details from item class by attribute ID.
     * @param attributeId the ID of the attribute to retrieve
     * @return attribute details as List of Objects or null if item class not found
     */
    private List getAttributeDetailsFromItemClass(String attributeId) {
        def inventoryItemClass = vismaConnectService.getItemClassById(ITEM_CLASS_ID as int)
        if (!inventoryItemClass) return null
        return inventoryItemClass.attributes?.find { it.attributeId == attributeId }?.details
    }


    /**
     * Gets categories from product attributes
     *
     * @param productAttributes as List
     * @return categories as List or null
     */
    List getCategoriesFromProductAttributes(List productAttributes) {
        return getAttributesById(productAttributes, CATEGORY_ATTRIBUTE_ID)
    }

/**
 * Gets subcategories from product attributes.
 *
 * @param productAttributes as List
 * @return subcategories as List or null
 */
    List getSubcategoriesFromProductAttributes(List productAttributes) {
        return getAttributesById(productAttributes, SUBCATEGORY_ATTRIBUTE_ID)
    }

/**
 * Gets sub2categories from product attributes
 * @param productAttributes as List of Object
 * @return sub2categories as List of Object
 */
    List getSub2categoriesFromProductAttributes(List productAttributes) {
        return getAttributesById(productAttributes, SUB2CATEGORY_ATTRIBUTE_ID)
    }

/**
 * Helper method to get attributes by ID
 * @param productAttributes as List
 * @param attributeId as String
 * @return filtered attributes as List or null
 */
    private List getAttributesById(List productAttributes, String attributeId) {
        if (!productAttributes) {
            return null
        }

        def attributes = productAttributes.findAll { it.id == attributeId }
        return attributes ?: null
    }

    /**
     * Getting categories & subcategories from product attributes
     * @param productAttributes as List of Object
     * @return categories & subcategories as List of Object
     */
    List getCategoriesAndSubcategoriesFromProductAttributes (List productAttributes) {
        if (productAttributes) {
            return productAttributes.findAll {
                it.id == CATEGORY_ATTRIBUTE_ID ||
                it.id == SUBCATEGORY_ATTRIBUTE_ID ||
                it.id == SUB2CATEGORY_ATTRIBUTE_ID
            }
        }
        return null
    }

    /**
     * Get a category by vismaId or create a new one if it doesn't exist
     * @param categoryObj as Object
     * @return category as Category
     */
    @Deprecated
    Category getCategoryByVismaId(def categoryObj) {
        String categoryName = categoryObj.description
        String categoryVismaId = categoryObj.value
        Category category = Category.findByVismaId(categoryVismaId)
        if (!category) {
            category = createNewCategory(categoryName, categoryVismaId)
        }
        return category
    }

    /**
     * Get a subcategory by vismaId or create a new one if it doesn't exist
     * @param subcategoryObj as Object
     * @return subcategory as Category
     */
    @Deprecated
    Category getSubcategoryByVismaId(def subcategoryObj) {
        String subcategoryName = subcategoryObj.description
        String subcategoryVismaId = subcategoryObj.value
        Category subcategory = Category.findByVismaId(subcategoryVismaId)
        if (!subcategory) {
            Category category = Category.findByVismaId(getCategoryVismaIdBySubcategory(subcategoryVismaId))
            if (category) {
                subcategory = createNewCategory(subcategoryName, subcategoryVismaId, category)
            }
        }
        return subcategory
    }

    /**
     * Get a category by vismaId or create a new one if it doesn't exist
     * @param categoryVismaId as String
     * @param categoryName as String (optional)
     * @return category as Category
     */
    Category getCategoryByVismaId(String categoryVismaId, String categoryName = null, Boolean forUpdate = false) {
        if (!categoryVismaId) return null
        Category category = Category.findByVismaId(categoryVismaId)
        if (!category && categoryName) {
            category = createNewCategory(categoryName, categoryVismaId)
            if (forUpdate) sendCategoriesBasedOnSalesCategories()
            category.refresh()
        }
        return category
    }

    /**
     * Get a category by name or create a new one if it doesn't exist
     * IMPORTANT!!! This works only for "Sales Categories"
     * @param categoryName: String
     * @return
     */
    @Deprecated
    Category getCategoryByName(String categoryName, Boolean forUpdate = false) {
        if (!categoryName) return null
        Category category = Category.findByName(categoryName)
        if (!category) {
            populateCategoriesFromSalesCategories()
            if (forUpdate) sendCategoriesBasedOnSalesCategories()
            category = Category.findByName(categoryName)
        }
        return category
    }

    /**
     * Get a subcategory by vismaId or create a new one if it doesn't exist
     * @param subcategoryVismaId as String
     * @param subcategoryName as String (optional)
     * @return subcategory as Category
     */
    Category getSubcategoryByVismaId(String subcategoryVismaId, String subcategoryName = null) {
        Category subcategory = Category.findByVismaId(subcategoryVismaId)
        if (!subcategory && subcategoryName) {
            Category category = Category.findByVismaId(getCategoryVismaIdBySubcategory(subcategoryVismaId))
            if (category) {
                subcategory = createNewCategory(subcategoryName, subcategoryVismaId, category)
            }
        }
        return subcategory
    }

    /**
     * Get a sub2category by vismaId or create a new one if it doesn't exist
     * @param sub2categoryVismaId as String
     * @param sub2categoryName as String (optional)
     * @return sub2category as Category
     */
    Category getSub2categoryByVismaId(String sub2categoryVismaId, String sub2categoryName = null) {
        Category sub2category = Category.findByVismaId(sub2categoryVismaId)
        if (!sub2category && sub2categoryName) {
            Category subcategory = Category.findByVismaId(getSubcategoryVismaIdBySub2category(sub2categoryVismaId))
            if (subcategory) {
                sub2category = createNewCategory(sub2categoryName, sub2categoryVismaId, subcategory)
            }
        }
        return sub2category
    }

    /**
     * Gets the parent category ID by truncating the child category ID to the specified length.
     *
     * @param childCategoryId as String
     * @param parentCategoryLength as Integer
     * @param expectedChildLength as Integer
     * @return The parent category ID or null
     */
    private String getParentCategoryId(String childCategoryId, int parentCategoryLength, int expectedChildLength) {
        if (childCategoryId != null && childCategoryId.length() == expectedChildLength) {
            return childCategoryId.take(parentCategoryLength)
        }
        return null
    }

    /**
     * Gets the category ID by extracting it from subcategory.
     *
     * @param subcategoryVismaId as String
     * @return The category ID or null
     */
    String getCategoryVismaIdBySubcategory(String subcategoryVismaId) {
        return getParentCategoryId(subcategoryVismaId, CATEGORY_LENGTH, SUBCATEGORY_LENGTH)
    }

    /**
     * Gets the subcategory ID by extracting it from the sub2category.
     *
     * @param sub2categoryVismaId as String
     * @return The subcategory ID or null
     */
    String getSubcategoryVismaIdBySub2category(String sub2categoryVismaId) {
        return getParentCategoryId(sub2categoryVismaId, SUBCATEGORY_LENGTH, SUB2CATEGORY_LENGTH)
    }

    /**
     * Sending unmapped(without wooId) categories to WooCommerce
     */
    void sendCategoriesToWoo() {
        println '\r\n---> Sending unmapped categories/subcategories to the WooCommerce...'
        switch (categorySource) {
            case CategorySource.ITEM_CLASS:
                sendCategoriesBasedOnItemClasses()
                break
            case CategorySource.SALES_CATEGORY:
                sendCategoriesBasedOnSalesCategories(true)
                break
        }

//        Category.withSession {
//            it.flush()
//            it.clear()
//        }
    }

    void sendCategoriesBasedOnItemClasses() {
        println("\r\n---> Starting to send unmapped categories to WooCommerce...")
        
        // 1. Process all unmapped top-level categories (CAT) and their subcategories recursively
        List<Category> unmappedTopCategories = Category.findAllByWooIdIsNullAndTypeAndParentCategoryIsNull(CategoryType.CAT)
        if (unmappedTopCategories) {
            log.info("Processing ${unmappedTopCategories.size()} unmapped top-level categories (CAT)")
            unmappedTopCategories.each { Category cat ->
                sendCategoryToWoo(cat)
            }
        } else {
            log.info("No unmapped top-level categories (CAT) found")
        }
        
        // 2. Process all unmapped subcategories (SUBCAT) and their subcategories recursively
        List<Category> unmappedSubcategories = Category.findAllByWooIdIsNullAndTypeAndParentCategoryIsNotNull(CategoryType.SUBCAT)
        if (unmappedSubcategories) {
            log.info("Processing ${unmappedSubcategories.size()} unmapped subcategories (SUBCAT)")
            unmappedSubcategories.each { Category subcat ->
                if (subcat.parentCategory?.wooId != null) {
                    sendCategoryToWoo(subcat, subcat.parentCategory.wooId)
                }
            }
        } else {
            log.info("No unmapped subcategories (SUBCAT) found")
        }
        
        // 3. Process all unmapped sub2categories (SUB2CAT)
        List<Category> unmappedSub2categories = Category.findAllByWooIdIsNullAndType(CategoryType.SUB2CAT)
        if (unmappedSub2categories) {
            log.info("Processing ${unmappedSub2categories.size()} unmapped sub2categories (SUB2CAT)")
            unmappedSub2categories.each { Category sub2cat ->
                if (sub2cat.parentCategory?.wooId != null) {
                    sendCategoryToWoo(sub2cat, sub2cat.parentCategory.wooId)
                }
            }
        } else {
            log.info("No unmapped sub2categories (SUB2CAT) found")
        }
        
        log.info("Completed sending unmapped categories to WooCommerce")
    }

    /**
     * Send a category to WooCommerce with WPML translation support
     * @param category as Category to send
     * @param parentWooId as Integer (Optional, parent category WooCommerce ID)
     * @param lang as Language (Optional language to send the category in)
     */
    private void sendCategoryToWoo(Category category, Integer parentWooId = null, Language lang = null) {
        if (category.wooId != null && lang == null) {
            if (appService.isMultilingual) {
                sendCategoryTranslationsToWoo(category)
            }
            return
        }

        // Map category data for WooCommerce API
        def data = wooConnectService.mapCategoryToWooApi(category, parentWooId, lang)

        // Determine if we're updating an existing category/translation or creating a new one
        boolean isUpdate = false
        Integer existingWooId = null

        if (lang) {
            // For translations, check if we already have a wooId in the CategoryText
            CategoryText categoryText = category.texts.find { it.lang == lang }
            if (categoryText?.wooId) {
                isUpdate = true
                existingWooId = categoryText.wooId
            }
        } else if (category.wooId) {
            // For main category, check if it already has a wooId
            isUpdate = true
            existingWooId = category.wooId
        }

        log.info("${isUpdate ? 'Updating' : 'Sending'} category '${category}' to WooCommerce${lang ? ' in language ' + lang : ''}")
        def wooCategoryObj = wooConnectService.sendCategoryToWoo(data, existingWooId)

        if (wooCategoryObj?.id) {
            if (lang) {
                // For translations, store the wooId in the CategoryText
                CategoryText categoryText = category.texts.find { it.lang == lang }
                if (categoryText) {
                    categoryText.wooId = wooCategoryObj.id as Integer
                    category.save(flush: true)
                    log.info("Updated translation wooId for category '${category}' in language ${lang}: ${categoryText.wooId}")
                }
            } else {
                // For main category, store the wooId in the Category
                category.wooId = wooCategoryObj.id as Integer
                category.save(flush: true)

                // If multilingual is enabled, send translations after the main category is created
                if (appService.isMultilingual) {
                    sendCategoryTranslationsToWoo(category)
                }

                // Process subcategories recursively (only for main language)
                category.subcategories?.findAll { it.wooId == null }?.each { subcat ->
                    sendCategoryToWoo(subcat, category.wooId)
                }
            }
        } else {
            log.error("Failed to ${isUpdate ? 'update' : 'send'} category '${category}' to WooCommerce${lang ? ' in language ' + lang : ''}")
        }
    }

    /**
     * Send translations of a category to WooCommerce using WPML
     * @param category as Category whose translations should be sent
     */
    private void sendCategoryTranslationsToWoo(Category category) {
        if (!category.wooId) {
            log.warn("Cannot send translations for category '${category}' without a WooCommerce ID")
            return
        }

        List<Tenant> tenants = tenantService.getAllNonMainTenants()

        tenants.each { Tenant tenant ->
            CategoryText categoryText = category.texts.find { it.lang == tenant.lang }
            CategoryText parentCategoryText = category.parentCategory?.texts?.find { it.lang == tenant.lang }

            if (!categoryText) {
                log.warn("Skipping translation for category '${category}' in language ${tenant.lang} - no translation found")
                return
            }

            sendCategoryToWoo(category, parentCategoryText?.wooId, tenant.lang)
        }

        // Process subcategories recursively
        category.subcategories?.findAll { it.wooId != null }?.each { subcat ->
            sendCategoryTranslationsToWoo(subcat)
        }
    }

    void sendCategoriesBasedOnSalesCategories(Boolean sessionClear = false) {
        List<Category> categories = Category.findAllWhere(parentCategory: null)
        if (!categories) {
            println 'No categories found for sending to Woo'
            return
        }

        def dataMap
        def wooCategoryObj
        Category.withSession {Session session ->
            categories.each {Category cat ->
                if (!cat.wooId) {
                    dataMap = wooConnectService.mapCategoryToWooApi(cat)
                    println "Sending category '${cat}' to the WooCommerce"
                    wooCategoryObj = wooConnectService.sendCategoryToWoo(dataMap)
                    cat.wooId = wooCategoryObj?.id ?: null
                    cat.save()
                }
                if (cat.subcategories && cat.wooId) {
                    List<Map<String, ?>> subcategories = [[
                            parentCategoryWooId: cat.wooId,
                            subcategiries: cat.subcategories
                    ]]
                    sendSubcategoriesBasedOnSalesCategories(subcategories)
                }
            }
            session.flush()
            if (sessionClear) session.clear()
        }
    }

    void sendSubcategoriesBasedOnSalesCategories(List<Map<String, ?>> subcategories = []) {
        if (!subcategories) return
        List<Map<String, ?>> currentSubcategories = []
        Category.withSession {Session session ->
            def dataMap
            def wooSubcategoryObj
            subcategories.each {Map<String, ?> subcatData ->
                subcatData.subcategiries.each {Category subcat ->
                    if (!subcat.wooId) {
                        dataMap = wooConnectService.mapCategoryToWooApi(subcat, subcatData.parentCategoryWooId as Integer)
                        println "Sending subcategory '${subcat}' to the WooCommerce"
                        wooSubcategoryObj = wooConnectService.sendCategoryToWoo(dataMap)
                        subcat.wooId = wooSubcategoryObj?.id ?: null
                        subcat.save()
                    }
                    if (subcat.subcategories && subcat.wooId) {
                        currentSubcategories << [
                                parentCategoryWooId: subcat.wooId,
                                subcategiries: subcat.subcategories
                        ]
                    }
                }
            }
            session.flush()
        }
        if (currentSubcategories) {
            sendSubcategoriesBasedOnSalesCategories(currentSubcategories)
        }
    }

    /**
     * Checking categories for updates in Product
     * @param product as Product
     * @param categories as List
     * @return updated as Boolean
     */
    @Transactional
    Boolean categoriesUpdated(Product product, List categories) {
        Boolean updated = false
        Category category

        switch (categorySource) {
            case CategorySource.ITEM_CLASS:
                // TODO: Should be added CREATE and DELETE functionality
                categories.each { catVismaObj ->
                    if (catVismaObj.id == CATEGORY_ATTRIBUTE_ID ||
                            catVismaObj.id == SUBCATEGORY_ATTRIBUTE_ID ||
                            catVismaObj.id == SUB2CATEGORY_ATTRIBUTE_ID) {
                        if (catVismaObj.value.toString().contains(',')) {
                            catVismaObj.value.toString().split(',').each { catVismaId ->
                                category = product.categories.find { it.vismaId == catVismaId }
                                if (!category) {
                                    category = Category.findByVismaId(catVismaId)
                                    if (category) {
                                        product.addToCategories(category).save()
                                        updated = true
                                    }
                                }
                            }
                        } else {
                            category = product.categories.find { it.vismaId == catVismaObj.value }
                            if (!category) {
                                category = Category.findByVismaId(catVismaObj.value as String)
                                if (category) {
                                    product.addToCategories(category).save()
                                    updated = true
                                }
                            }
                        }
                    }
                }
                break
            case CategorySource.SALES_CATEGORY:
                categories.each { Object productSalesCategory ->
                    category = product.categories.find { it.vismaId == productSalesCategory.categoryId as String }
                    if (!category) {
                        category = getCategoryByVismaId(productSalesCategory.categoryId as String, productSalesCategory.description as String, true)
                        if (category) {
                            product.addToCategories(category).save()
                            updated = true
                        }
                    }
                }
                // checking removed categories in the product
                if (!categories && product.categories?.size() > 0) {
                    product.categories.clear()
                    updated = true
                } else if (categories && categories?.size() != product.categories?.size()) {
                    product.categories.removeAll { Category productCategory ->
                        !categories.find { it.categoryId == productCategory.name }
                    }
                    updated = true
                }
                break
        }
        return updated
    }

    @Transactional
    void checkForSalesCategoryUpdates() {
        categoriesToDelete = []
        List<Category> topCategoriesToDelete = Category.findAllWhere(parentCategory: null)
        List<Map<String, ?>> salesCategories = vismaConnectService.getSalesCategories() as List

        if (!salesCategories) {
            println 'No SalesCategories to check for updates'
            return
        }

        Category.withSession { Session session ->
            Category category
            Map<String, ?> wooCategoryObj
            Map<String, ?> categoryData = [:]

            salesCategories.each { Map<String, ?> salesCategory ->
                category = Category.findByVismaId(salesCategory.categoryID as String)
                topCategoriesToDelete -= category

                // create a new top category if it doesn't exist
                if (!category) {
                    categoryData = [
                            name: salesCategory.description,
                            vismaId: salesCategory.categoryID,
                            sortOrder: salesCategory.sortOrder,
                            type: CategoryType.CAT
                    ]
                    category = createNewCategory(categoryData)
                    if (!category) { return }

                    println "Sending category '${category}' to the WooCommerce"
                    wooCategoryObj = wooConnectService.sendCategoryToWoo(wooConnectService.mapCategoryToWooApi(category))
                    category.wooId = wooCategoryObj?.id as Integer ?: null
                    category.save()
                } else if (categoryUpdated(category, salesCategory)) {
                    println "Sending category '${category}' to the WooCommerce"
                    wooConnectService.sendCategoryToWoo(wooConnectService.mapCategoryToWooApi(category), category.wooId)
                }

                // checking for subcategories
                if (category && salesCategory.subCategories) {
                    List<Map<String, ?>> subcategories = [[
                              parentCategory: category,
                              subcategiries: salesCategory.subCategories
                      ]]
                    checkForSalesSubcategoryUpdates(subcategories)
                }
            }

            if (topCategoriesToDelete) {
                clearProductsFromCategories(topCategoriesToDelete)
                categoriesToDelete += topCategoriesToDelete
            }

            session.flush()
            session.clear()

            if (categoriesToDelete) {
                deleteCategories(categoriesToDelete.collect { it.id })
            }
        }
    }

    /**
     * Checking for updates to subcategories recursively
     */
    @Transactional
    void checkForSalesSubcategoryUpdates(List<Map<String, ?>> subcategories = []) {
        if (!subcategories) { return }

        Category parentCategory
        Category subcategory
        Map<String, ?> wooSubcategoryObj
        List<Category> subcategoriesToDelete = []
        List<Map<String, ?>> currentSubcategories = []

        Category.withSession { Session session ->
            subcategories.each { Map<String, ?> subcatData ->

                parentCategory = subcatData.parentCategory as Category
                subcategoriesToDelete = parentCategory.subcategories as List<Category>

                subcatData.subcategiries.each { Map<String, ?> subcategoryVismaObj ->
                    subcategory = Category.createCriteria().get {
                        eq("vismaId", subcategoryVismaObj.categoryID as String)
                        eq("parentCategory", parentCategory)
                    } as Category
                    if (subcategoriesToDelete) {
                        subcategoriesToDelete -= subcategory
                    }

                    // create a new subcategory if it doesn't exist
                    if (!subcategory) {
                        Map<String, ?> subcategoryData = [
                                name: subcategoryVismaObj.description,
                                vismaId: subcategoryVismaObj.categoryID,
                                sortOrder: subcategoryVismaObj.sortOrder,
                                type: CategoryType.SUBCAT
                        ]
                        subcategory = createNewCategory(subcategoryData as Map, subcatData.parentCategory as Category)
                        if (!subcategory) { return }

                        println "Sending subcategory '${subcategory}' to the WooCommerce"
                        wooSubcategoryObj = wooConnectService.sendCategoryToWoo(wooConnectService.mapCategoryToWooApi(subcategory, subcatData.parentCategory?.wooId as Integer))
                        subcategory.wooId = wooSubcategoryObj?.id ?: null
                        subcategory.save()
                    } else if (categoryUpdated(subcategory, subcategoryVismaObj)) {
                        println "Sending subcategory '${subcategory}' to the WooCommerce"
                        wooSubcategoryObj = wooConnectService.sendCategoryToWoo(wooConnectService.mapCategoryToWooApi(subcategory, subcatData.parentCategory?.wooId as Integer), subcategory.wooId)
                    }

                    if (subcategory && subcategoryVismaObj.subCategories) {
                        currentSubcategories << [
                                parentCategory: subcategory,
                                subcategiries: subcategoryVismaObj.subCategories
                        ]
                    }
                }
                if (subcategoriesToDelete) {
                    clearProductsFromCategories(subcategoriesToDelete)
                    categoriesToDelete += subcategoriesToDelete
                }
            }
            session.flush()
        }
        if (currentSubcategories) {
            checkForSalesSubcategoryUpdates(currentSubcategories)
        }
    }

    @Transactional
    void clearProductsFromCategories(List<Category> categoriesToDelete) {
        List<Product> productsToUpdate
        List<Category> subcategoriesToDelete = []

        Product.withSession { Session session ->
            categoriesToDelete.each { Category categoryToDelete ->

                productsToUpdate = Product.withCriteria {
                    categories {
                        idEq(categoryToDelete.id)
                    }
                } as List<Product>

                if (productsToUpdate) {
                    productsToUpdate.each { Product product ->
                        product.removeFromCategories(categoryToDelete)
                    }
                }

                if (categoryToDelete.subcategories) {
                    subcategoriesToDelete += categoryToDelete.subcategories
                }
            }
            session.flush()
        }

        if (subcategoriesToDelete) {
            clearProductsFromCategories(subcategoriesToDelete)
        }
    }

    @Transactional
    void deleteCategories(List<Long> categoryIds) {
        Category.withSession { Session session ->
            Category category
            categoryIds.each { Long categoryId ->
                category = Category.get(categoryId)
                if (!category) {
                    return
                } else if (!category.wooId || (category.wooId && wooConnectService.deleteCategoryFromWoo(category.wooId))) {
                    category.delete()
                }
            }
            session.flush()
            session.clear()
        }
    }

    @Transactional
    void mapUnmappedCategoriesToWoo() {
        List<Category> unmappedCategories = Category.findAllWhere(wooId: null)
        List<Map> wooCategories = wooConnectService.getCategories() as List<Map>

        if (!unmappedCategories) {
            throw new CategoryUpdateException("", "There are no unmapped categories")
        } else if (!wooCategories) {
            throw new CategoryUpdateException("", "There are no categories in Woo")
        }

        Category.withSession { Session session ->
            Integer categoryWooId
            unmappedCategories.each { Category category ->
                try {
                    categoryWooId = wooCategories.find { it.name == category.name }?.id as Integer
                } catch (Exception e) {
                    println("ERROR: an error occurred while mapping unmapped categories '$e.message'")
                }
                if (categoryWooId) {
                    category.wooId = categoryWooId
                }
            }
            session.flush()
            session.clear()
        }
    }

    @Transactional
    void mapUnmappedCategoriesFromCsv(String filePath) {
        List<Category> unmappedCategories = Category.findAllWhere(wooId: null)
        if (!unmappedCategories) {
            throw new CategoryUpdateException("", "There are no unmapped categories")
        }

        File file = new File(filePath)
        if (!file.exists()) {
            throw new CategoryUpdateException("", "File with categories not found: $filePath")
        }

        Map<String, Integer> csvMap = [:]
        file.withReader { reader ->
            CSVReader csvReader = new CSVReader(reader)
            csvReader.forEach { line ->
                if (line.length >= 2) {
                    String key = line[0]?.trim()
                    Integer value = line[1]?.trim()?.isInteger() ? line[1]?.trim().toInteger() : null
                    csvMap[key] = value
                }
            }
        }

        if (!csvMap) {
            throw new CategoryUpdateException("", "There are no categories from File")
        }

        Category.withSession { Session session ->
            Integer categoryWooId
            unmappedCategories.each { Category category ->
                try {
                    categoryWooId = csvMap[category.name]
                    if (categoryWooId) {
                        category.wooId = categoryWooId
                    }
                } catch (Exception e) {
                    println("ERROR: An error occurred while mapping unmapped categories: ${e.message}")
                }
            }
            session.flush()
            session.clear()
        }
    }

    @Transactional
    void deleteUnmappedCategoriesFromWoo() {
        List<Map> wooCategories = wooConnectService.getCategories() as List<Map>
        Set<Integer> categoryIds = Category.withCriteria {
            isNotNull("wooId")
            projections {
                property("wooId")
            }
        } as Set<Integer>

        if (!wooCategories) {
            throw new CategoryUpdateException("", "There are no categories in Woo")
        } else if (!categoryIds) {
            throw new CategoryUpdateException("", "There are no mapped categories in System")
        }

        Set<Integer> wooCategoryIdsToDelete = []
        Set<Integer> wooCategoryIds = wooCategories.collect{ it.id } as Set<Integer>
        wooCategoryIds.each { Integer wooCategoryId ->
            if (!categoryIds.contains(wooCategoryId) && wooCategoryId != DEFAULT_CATEGORY_ID) {
                wooCategoryIdsToDelete.add(wooCategoryId)
            }
        }

        // delete from Woo
        if (wooCategoryIdsToDelete) {
            wooCategoryIdsToDelete.each { Integer wooCategoryIdToDelete ->
                wooConnectService.deleteCategoryFromWoo(wooCategoryIdToDelete)
            }
        }
    }

    Boolean categoryUpdated(Category category, Object categoryVismaObj) {
        if (!category || !categoryVismaObj) { return false }
        Boolean updated = false
        updated = updateService.fieldUpdated(category, 'name', categoryVismaObj.description) || updated
        updated = updateService.fieldUpdated(category, 'sortOrder', categoryVismaObj.sortOrder) || updated
        updated
    }

    @Transactional
    void syncAllCategories() {
        List<Category> categories = Category.list().findAll { it.wooId }

        if (!categories) {
            throw new CategoryUpdateException("", "There are no mapped categories in System")
        }

        def result
        Category.withSession { Session session ->
            categories.each { Category category ->
                result = wooConnectService.sendCategoryToWoo(wooConnectService.mapCategoryToWooApi(category, category.parentCategory?.wooId), category.wooId)
                if (!result) {
                    category.wooId = null
                }
            }
            session.flush()
            session.clear()
        }
    }

    /**
     * Checks for updates to categories created from item classes
     */
    @Transactional
    void checkForItemClassCategoryUpdates() {
        categoriesToDelete = []

        List vismaCategories = getCategoriesFromItemClass()
        List vismaSubcategories = getSubcategoriesFromItemClass()
        List vismaSub2categories = getSub2categoriesFromItemClass()

        if (!vismaCategories) {
            log.warn('No categories found in Visma item classes')
            return
        }

        List<Category> existingCategories = Category.findAllWhere(parentCategory: null, type: CategoryType.CAT)
        List<Category> categoriesToRemove = existingCategories.findAll { existingCat ->
            !vismaCategories.find { it.id == existingCat.vismaId }
        }

        Category.withTransaction { status ->
            try {
                vismaCategories.each { vismaCat ->
                    Category category = Category.findByVismaId(vismaCat.id as String)

                    if (!category) {
                        category = findOrCreateCategory(vismaCat.description, vismaCat.id, null)
                        if (category) {
                            log.info("Created new category: ${category.name}")

                            def wooCategoryObj = wooConnectService.sendCategoryToWoo(
                                wooConnectService.mapCategoryToWooApi(category)
                            )
                            if (wooCategoryObj?.id) {
                                category.wooId = wooCategoryObj.id as Integer
                                category.save()
                            }
                        }
                    } else if (categoryUpdated(category, vismaCat)) {
                        log.info("Updated category: ${category.name}")

                        if (category.wooId) {
                            wooConnectService.sendCategoryToWoo(
                                wooConnectService.mapCategoryToWooApi(category),
                                category.wooId
                            )
                        }
                    }

                    if (category) {
                        processItemClassSubcategoryUpdates(
                            category,
                            vismaSubcategories,
                            vismaSub2categories
                        )
                    }
                }

                if (categoriesToRemove) {
                    clearProductsFromCategories(categoriesToRemove)
                    categoriesToDelete.addAll(categoriesToRemove)
                }

                if (categoriesToDelete) {
                    deleteCategories(categoriesToDelete.collect { it.id })
                }

            } catch (Exception e) {
                status.setRollbackOnly()
                log.error("Failed to check for item class category updates: ${e.message}", e)
                throw e
            }
        }
    }

    /**
     * Processes updates to subcategories created from item classes
     * @param parentCategory as Category
     * @param vismaSubcategories as List of subcategories from Visma
     * @param vismaSub2categories as List of sub2categories from Visma
     */
    private void processItemClassSubcategoryUpdates(
        Category parentCategory,
        List vismaSubcategories,
        List vismaSub2categories
    ) {
        def relevantSubcategories = vismaSubcategories.findAll {
            it.parentId == parentCategory.vismaId
        }

        List<Category> existingSubcategories = parentCategory.subcategories as List<Category>
        List<Category> subcategoriesToRemove = existingSubcategories.findAll { existingSubcat ->
            !relevantSubcategories.find { it.id == existingSubcat.vismaId }
        }

        relevantSubcategories.each { vismaSubcat ->
            Category subcategory = Category.findByVismaIdAndParentCategory(
                vismaSubcat.id as String,
                parentCategory
            )

            if (!subcategory) {
                subcategory = findOrCreateCategory(vismaSubcat.description, vismaSubcat.id, parentCategory)
                if (subcategory) {
                    log.info("Created new subcategory: ${subcategory.name}")

                    def wooSubcategoryObj = wooConnectService.sendCategoryToWoo(
                        wooConnectService.mapCategoryToWooApi(
                            subcategory,
                            parentCategory.wooId
                        )
                    )
                    if (wooSubcategoryObj?.id) {
                        subcategory.wooId = wooSubcategoryObj.id as Integer
                        subcategory.save()
                    }
                }
            } else if (categoryUpdated(subcategory, vismaSubcat)) {
                log.info("Updated subcategory: ${subcategory.name}")

                if (subcategory.wooId) {
                    wooConnectService.sendCategoryToWoo(
                        wooConnectService.mapCategoryToWooApi(
                            subcategory,
                            parentCategory.wooId
                        ),
                        subcategory.wooId
                    )
                }
            }

            if (subcategory) {
                def relevantSub2categories = vismaSub2categories.findAll {
                    it.parentId == subcategory.vismaId
                }

                List<Category> existingSub2categories = subcategory.subcategories as List<Category>
                List<Category> sub2categoriesToRemove = existingSub2categories.findAll { existingSub2cat ->
                    !relevantSub2categories.find { it.id == existingSub2cat.vismaId }
                }

                relevantSub2categories.each { vismaSub2cat ->
                    Category sub2category = Category.findByVismaIdAndParentCategory(
                        vismaSub2cat.id as String,
                        subcategory
                    )

                    if (!sub2category) {
                        sub2category = findOrCreateCategory(vismaSub2cat.description, vismaSub2cat.id, subcategory)
                        if (sub2category) {
                            log.info("Created new sub2category: ${sub2category.name}")

                            def wooSub2categoryObj = wooConnectService.sendCategoryToWoo(
                                wooConnectService.mapCategoryToWooApi(
                                    sub2category,
                                    subcategory.wooId
                                )
                            )
                            if (wooSub2categoryObj?.id) {
                                sub2category.wooId = wooSub2categoryObj.id as Integer
                                sub2category.save()
                            }
                        }
                    } else if (categoryUpdated(sub2category, vismaSub2cat)) {
                        log.info("Updated sub2category: ${sub2category.name}")

                        if (sub2category.wooId) {
                            wooConnectService.sendCategoryToWoo(
                                wooConnectService.mapCategoryToWooApi(
                                    sub2category,
                                    subcategory.wooId
                                ),
                                sub2category.wooId
                            )
                        }
                    }
                }

                if (sub2categoriesToRemove) {
                    clearProductsFromCategories(sub2categoriesToRemove)
                    categoriesToDelete.addAll(sub2categoriesToRemove)
                }
            }
        }

        if (subcategoriesToRemove) {
            clearProductsFromCategories(subcategoriesToRemove)
            categoriesToDelete.addAll(subcategoriesToRemove)
        }
    }

    /**
     * Deletes all mapped categories from WooCommerce and resets their wooId
     * Also removes category translations
     */
    @Transactional
    void deleteAllMappedCategoriesFromWoo() {
        List<Category> mappedCategories = Category.findAllByWooIdIsNotNull()
        
        if (!mappedCategories) {
            throw new CategoryUpdateException("", "There are no mapped categories in the system")
        }
        
        log.info("Found ${mappedCategories.size()} mapped categories to delete from WooCommerce")
        
        // Process in reverse order: SUB2CAT -> SUBCAT -> CAT to avoid parent-child dependency issues
        List<Category> sub2Categories = mappedCategories.findAll { it.type == CategoryType.SUB2CAT }
        List<Category> subCategories = mappedCategories.findAll { it.type == CategoryType.SUBCAT }
        List<Category> topCategories = mappedCategories.findAll { it.type == CategoryType.CAT }
        
        // Process categories in batches to avoid overwhelming the API
        Category.withTransaction { status ->
            try {
                // First delete SUB2CAT
                if (sub2Categories) {
                    log.info("Deleting ${sub2Categories.size()} SUB2CAT categories")
                    deleteCategoriesFromWoo(sub2Categories)
                }
                
                // Then delete SUBCAT
                if (subCategories) {
                    log.info("Deleting ${subCategories.size()} SUBCAT categories")
                    deleteCategoriesFromWoo(subCategories)
                }
                
                // Finally delete CAT
                if (topCategories) {
                    log.info("Deleting ${topCategories.size()} CAT categories")
                    deleteCategoriesFromWoo(topCategories)
                }
                
                log.info("All mapped categories have been deleted from WooCommerce")
            } catch (Exception e) {
                status.setRollbackOnly()
                log.error("Error deleting mapped categories: ${e.message}")
                throw new CategoryUpdateException("", "Error deleting mapped categories: ${e.message}")
            }
        }
    }

    /**
     * Helper method to delete categories from WooCommerce and reset their wooId
     */
    private void deleteCategoriesFromWoo(List<Category> categories) {
        categories.each { Category category ->
            // Delete translations first if multilingual is enabled
            if (appService.isMultilingual && category.texts) {
                category.texts.each { CategoryText text ->
                    if (text.wooId) {
                        boolean success = wooConnectService.deleteCategoryFromWoo(text.wooId)
                        if (success) {
                            log.info("Deleted translation for category '${category.name}' in language ${text.lang}")
                            text.wooId = null
                        }
                    }
                }
            }
            
            // Then delete the main category
            if (category.wooId) {
                boolean success = wooConnectService.deleteCategoryFromWoo(category.wooId)
                if (success) {
                    log.info("Deleted category '${category.name}' with wooId ${category.wooId}")
                    category.wooId = null
                    category.save(flush: true)
                } else {
                    log.warn("Failed to delete category '${category.name}' with wooId ${category.wooId}")
                }
            }
        }
    }

}
