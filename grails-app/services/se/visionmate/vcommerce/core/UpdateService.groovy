package se.visionmate.vcommerce.core

import grails.gorm.transactions.Transactional
import grails.util.Environment
import groovy.time.TimeCategory
import se.visionmate.vcommerce.enums.PacketOption
import se.visionmate.vcommerce.enums.UpdateType
import se.visionmate.vcommerce.exception.CategoryUpdateException
import se.visionmate.vcommerce.exception.CustomerUpdateException
import se.visionmate.vcommerce.exception.DiscountUpdateException
import se.visionmate.vcommerce.exception.PriceUpdateException
import se.visionmate.vcommerce.exception.ProductUpdateException

class UpdateService {
    private static Boolean regularUpdate, fullUpdate
    private static Boolean canUpdate
    private static Timer timer
    private Date startDate
    private List<String> errors = []

    AppService appService
    PacketService packetService
    ProductService productService
    PriceService priceService
    CustomerService customerService
    OptionService optionService
    ErrorHandlerService errorHandlerService
    CategoryService categoryService

    void initialize() {
        Map<String, String> options = optionService.commonOptions
        regularUpdate = Boolean.parseBoolean(options?.regular_update)
        fullUpdate = Boolean.parseBoolean(options?.full_update)
        if (regularUpdate) {
            timer = new Timer()
            canUpdate = true
            initUpdateListener()
        }
    }

    void startRegularUpdate() {
        if (regularUpdate) {
            timer?.cancel()
            timer?.purge()
        }
        timer = new Timer()
        fullUpdate = false
        regularUpdate = true
        canUpdate = true
        initUpdateListener()
    }

    @Deprecated
    void pauseRegularUpdate() {
        regularUpdate = false
    }

    void stopRegularUpdate() {
        if (regularUpdate) {
            timer?.cancel()
            timer?.purge()
            regularUpdate = false
            canUpdate = false
        }
    }

    Boolean getRegularUpdate() {
        regularUpdate
    }

    Boolean getFullUpdate() {
        fullUpdate
    }

    void setCanUpdate(Boolean canUpdate) {
        this.canUpdate = canUpdate
    }

    void performFullUpdate() {
        if (regularUpdate) {
            stopRegularUpdate()
            fullUpdate = true
            checkForUpdates(true)
            fullUpdate = false
            startRegularUpdate()
        } else {
            fullUpdate = true
            checkForUpdates(true)
            fullUpdate = false
        }
    }

    /**
     * Deleting invalid data from Woo and local DB
     */
    void deleteInvalidData() {
        if(!fullUpdate) {
            if (regularUpdate) {
                stopRegularUpdate()
                productService.deleteInvalidProducts()
                startRegularUpdate()
            } else {
                productService.deleteInvalidProducts()
            }
        }
    }

    /**
     * Listener for regular updates
     */
    void initUpdateListener() {
        timer.schedule({
            if (appService.debugMode) {
                println"\r\nThe timer is still working"
            }
            if (regularUpdate && !fullUpdate) {
                checkForUpdates()
            }
        } as TimerTask, 1000, 60000)
    }

    /**
     * Main update check method
     */
    void checkForUpdates(Boolean performFullUpdate = false) {
        if (canUpdate || performFullUpdate) {
            startDate = new Date()
            canUpdate = false
            println "\r\n[$appService.timeStamp] Trying to perform ${fullUpdate ? 'Full' : 'Regular'} update"
            //---
            if (packetService.isActivePacketOption(PacketOption.SALES_CATEGORIES)) {
                try {
                    println '---> Checking for category updates...'
                    categoryService.checkForSalesCategoryUpdates()
                } catch (CategoryUpdateException e) {
                    errors << e.errorMessage
                    e.log()
                } catch (Exception e) {
                    errors << errorHandlerService.getErrorDetails('CATEGORY-UPDATE-ERROR', e)
                    errorHandlerService.logErrorDetails('CATEGORY-UPDATE-ERROR', e)
                }
            }
            //---
            try {
                println '---> Checking for product updates...'
                productService.checkForProductUpdates(fullUpdate ? UpdateType.FULL : UpdateType.REGULAR)
            } catch (ProductUpdateException e) {
                errors << e.errorMessage
                e.log()
            } catch (Exception e) {
                errors << errorHandlerService.getErrorDetails('PRODUCT-UPDATE-ERROR', e)
                errorHandlerService.logErrorDetails('PRODUCT-UPDATE-ERROR', e)
            }
            //---
            if (!fullUpdate) {
                try {
                    println '...Checking for stock updates...'
                    productService.checkForProductUpdates(UpdateType.STOCK)
                } catch (Exception e) {
                    errors << errorHandlerService.getErrorDetails('STOCK-UPDATE-ERROR', e)
                    errorHandlerService.logErrorDetails('STOCK-UPDATE-ERROR', e)
                }

                try {
                    println '...Checking for attachment updates...'
                    productService.checkForProductUpdates(UpdateType.ATTACHMENT)
                } catch (Exception e) {
                    errors << errorHandlerService.getErrorDetails('ATTACHMENT-UPDATE-ERROR', e)
                    errorHandlerService.logErrorDetails('ATTACHMENT-UPDATE-ERROR', e)
                }
            }
            //---
            try {
                println '---> Checking for customer updates...'
                customerService.checkForUpdates(lastModifiedDateTime)
            } catch (CustomerUpdateException e) {
                errors << e.errorMessage
                e.log()
            } catch (Exception e) {
                errors << errorHandlerService.getErrorDetails('CUSTOMER-UPDATE-ERROR', e)
                errorHandlerService.logErrorDetails('CUSTOMER-UPDATE-ERROR', e)
            }
            //---
            if (!fullUpdate) {
                if (packetService.isActivePacketOption(PacketOption.CONTACTS)) {
                    try {
                        println '...Checking for customer contact updates...'
                        customerService.checkForCustomerContactsUpdates(lastModifiedDateTime)
                    } catch (Exception e) {
                        errors << errorHandlerService.getErrorDetails('CUSTOMER_CONTACT-UPDATE-ERROR', e)
                        errorHandlerService.logErrorDetails('CUSTOMER_CONTACT-UPDATE-ERROR', e)
                    }
                }
                if (packetService.isActivePacketOption(PacketOption.LOCATIONS)) {
                    try {
                        println '...Checking for customer location updates...'
                        customerService.checkForCustomerLocationsUpdates(lastModifiedDateTime)
                    } catch (Exception e) {
                        errors << errorHandlerService.getErrorDetails('CUSTOMER_LOCATION-UPDATE-ERROR', e)
                        errorHandlerService.logErrorDetails('CUSTOMER_LOCATION-UPDATE-ERROR', e)
                    }
                }
            }
            //---
            try {
                println '---> Checking for price updates...'
                priceService.checkForPriceUpdates(lastModifiedDateTime)
            } catch (PriceUpdateException e) {
                errors << e.errorMessage
                e.log()
            } catch (Exception e) {
                errors << errorHandlerService.getErrorDetails('PRICE-UPDATE-ERROR', e)
                errorHandlerService.logErrorDetails('PRICE-UPDATE-ERROR', e)
            }
            //---
            if (packetService.isActivePacketOption(PacketOption.CUSTOMER_DISCOUNT)) {
                try {
                    println '---> Checking for discount updates...'
                    priceService.checkForDiscountUpdates(lastModifiedDateTime)
                } catch (DiscountUpdateException e) {
                    errors << e.errorMessage
                    e.log()
                } catch (Exception e) {
                    errors << errorHandlerService.getErrorDetails('DISCOUNT-UPDATE-ERROR', e)
                    errorHandlerService.logErrorDetails('DISCOUNT-UPDATE-ERROR', e)
                }
            }
            //---
            println "UPDATE-SUCCESS: ${TimeCategory.minus(new Date(), startDate)}"
            //---
            if (performFullUpdate) {
//                    println "---> Check for deleted products..."
//                    productService.deleteInvalidProducts()
                // Note: updateProductsInWoo is already called within checkForProductUpdates for products that need updates
                // No need to call it again here as it would cause duplicate processing
            }
            //---
            if (errors) {
                errorHandlerService.sendServiceErrorEmail('UPDATE-ERROR', errors)
                errors = []
            }
            //---
            canUpdate = true
        } else {
            println '\r\nPrevious update process is still in progress'
        }
    }

    /**
     * Getting LastModifiedDateTime for Visma
     * @return Date as String in Visma format
     * TODO: Hardcoded 12 hours, must be changed
     */
    String getLastModifiedDateTime() {
        use(TimeCategory) {
            if (fullUpdate) {
                return null
            } else if (regularUpdate) {
                return (new Date() - 12.hours).format('yyyy-MM-dd HH:mm:ss').replace(' ', 'T')
            } else {
                return (new Date() - 2.days).format('yyyy-MM-dd HH:mm:ss').replace(' ', 'T')
            }
        }
    }

    /**
     * Checking a field for update
     * @param object as Object (Domain class)
     * @param oldValue as Object
     * @param newValue as Object
     * @return Boolean
     */
    @Transactional
    Boolean fieldUpdated(def object, def fieldName, def newValue) {
        if (!object) { return false }
        if (!object."$fieldName" && !newValue) { return false }

        switch (object."$fieldName") {
            case Boolean:
                if (object."$fieldName" != newValue as Boolean) {
                    object."$fieldName" = newValue as Boolean
                    return true
                }
                return false
            case String:
                if (object."$fieldName".trim() != newValue.toString().trim()) {
                    object."$fieldName" = newValue as String
                    return true
                }
                return false
            case Integer:
                if (object."$fieldName" != newValue as Integer) {
                    object."$fieldName" = newValue as Integer
                    return true
                }
                return false
            case Long:
                if (object."$fieldName" != newValue as Long) {
                    object."$fieldName" = newValue as Long
                    return true
                }
                return false
            case BigDecimal:
                if (object."$fieldName" != newValue as BigDecimal) {
                    object."$fieldName" = newValue as BigDecimal
                    return true
                }
                return false
            case null:
                object."$fieldName" = newValue
                return true
            default:
                return false
        }
    }
}
