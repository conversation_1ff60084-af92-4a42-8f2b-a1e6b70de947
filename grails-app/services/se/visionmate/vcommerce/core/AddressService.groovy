package se.visionmate.vcommerce.core

import grails.gorm.transactions.Transactional
import grails.util.Environment
import se.visionmate.vcommerce.enums.AddressType

class AddressService {

    AppService appService
    UpdateService updateService

    /**
     * Creating a new address
     * @param type as AddressType
     * @param data as Object
     * @param entity as Object
     * @param flush as Boolean(optional)
     */
    @Transactional
    void createNewAddress(AddressType type, def entity, def data, Boolean flush = false) {
        Address address = new Address(
                type: type,
                postalCode: data.postalCode,
                addressLine1: data.addressLine1,
                addressLine2: data.addressLine2,
                city: data.city,
                county: data.county?.name,
                country: data.country?.name,
                countryCode: data.country?.id
        )
        switch (entity) {
            case Customer:
                address.customer = entity
                break
            case Location:
                address.location = entity
                break
        }
        try {
            if (address.validate()) {
                if (address.save(flush: flush)) {
                    println "[$appService.timeStamp] $address - has been created"
                } else {
                    println 'An error occurred while creating a new address'
                    if (appService.debugMode) {
                        address.errors.allErrors.each { println it }
                    }
                }
            } else {
                println 'Validation issue occurred while creating a new address'
                if (appService.debugMode) {
                    address.errors.allErrors.each { println it }
                }
            }
        } catch (Exception ex) {
            println ex.message
        }

    }

    /**
     * Checking for a new address
     * If doesnt exist create a new one
     * @param type as AddressType
     * @param entity as Object
     */
    void checkForNewAddress(AddressType type, def entity, def data) {
        switch (entity) {
            case Customer:
                if (!Address.findByTypeAndCustomer(type, entity)) {
                    createNewAddress(type, entity, data)
                }
                break
            case Location:
                if (!Address.findByTypeAndLocation(type, entity)) {
                    createNewAddress(type, entity, data)
                }
                break
        }
    }

    /**
     * Check for address updates
     * @param address as Address
     * @param addressVismaObj as Object
     * @return bool
     */
    Boolean addressUpdated(Address address, def addressVismaObj) {
        Boolean updated = false
        updated = updateService.fieldUpdated(address, 'postalCode', addressVismaObj?.postalCode) || updated
        updated = updateService.fieldUpdated(address, 'addressLine1', addressVismaObj?.addressLine1) || updated
        updated = updateService.fieldUpdated(address, 'addressLine2', addressVismaObj?.addressLine2) || updated
        updated = updateService.fieldUpdated(address, 'city', addressVismaObj?.city) || updated
        updated = updateService.fieldUpdated(address, 'country', addressVismaObj?.country?.name) || updated
        updated = updateService.fieldUpdated(address, 'countryCode', addressVismaObj?.country?.id) || updated
        updated
    }
}
