package se.visionmate.vcommerce.core

import grails.gorm.transactions.Transactional
import grails.plugins.mail.MailService
import grails.util.Environment
import org.hibernate.Session
import se.visionmate.vcommerce.api.VismaConnectService
import se.visionmate.vcommerce.api.WooConnectService
import se.visionmate.vcommerce.enums.CategorySource
import se.visionmate.vcommerce.enums.Language
import se.visionmate.vcommerce.enums.PriceClassType
import se.visionmate.vcommerce.enums.ProductStatus
import se.visionmate.vcommerce.enums.ProductType
import se.visionmate.vcommerce.enums.UpdateType
import se.visionmate.vcommerce.exception.ProductUpdateException
import se.visionmate.vcommerce.visma.Tenant
import se.visionmate.vcommerce.visma.TenantService

class ProductService {

    static Boolean vismaProductsPaging
    static String VARIATION_ATTRIBUTE_ID

    AppService appService
    OptionService optionService
    VismaConnectService vismaConnectService
    WooConnectService wooConnectService
    CategoryService categoryService
    AttributeService attributeService
    AttachmentService attachmentService
    UpdateService updateService
    TagService tagService
    PriceService priceService
    MailService mailService
    ErrorHandlerService errorHandlerService
    TenantService tenantService

    void initialize() {
        Map<String, String> options = optionService.vismaOptions
        vismaProductsPaging = Boolean.parseBoolean(options?.visma_products_paging)
        VARIATION_ATTRIBUTE_ID = options?.visma_variation_attribute_id
    }

    /**
     * Getting all products from Visma and putting to the local DB
     */
    void populateProducts(Integer pageNumber = 1, Integer pageSize = 500) {
        if (!vismaProductsPaging) { pageSize = 100 }

        List products = []
        try {
            products = vismaConnectService.getProducts([pageNumber:pageNumber, pageSize:pageSize])
        } catch (Exception e) {
            errorHandlerService.logErrorDetails('PRODUCT-POPULATE-ERROR', 'While getting products from Visma.NET', e)
        }

        if (!products) {
            log.warn('No products were found in Visma')
            return
        }

        List attachments, categories, subcategories, sub2categories, attributeTerms, tags
        Map<String, ?> data = [:]
        Map<Language, String> descriptions = [:]
        Product product
        Attribute attribute
        AttributeTerm attributeTerm
        Tag tag
        String tagName, tagDescription
        getProductsWithValidStatus(products)?.each { productObj ->
            attachments = productObj.attachments ?: []
            attributeTerms = attributeService.getAttributeTermsFromProductAttributes(productObj.attributes as List)
            tags = tagService.getTagsFromProductAttributes(productObj.attributes as List)
            descriptions = attributeService.getDescriptionsByLangFromProductAttributes(productObj.attributes as List)

            data.productNumber = productObj.inventoryNumber
            data.vismaId = productObj.inventoryId
            data.description = productObj.description
            data.htmlDescription = productObj.body
            data.productClass = productObj.itemClass?.description
            data.itemClassId = productObj.itemClass?.id
            data.availability = getAvailability(productObj.warehouseDetails as List)
            data.groupNumber = getGroupNumber(productObj.attributes as List)
            data.status = getProductStatus(productObj.status as String)
            data.type = getProductType(productObj.type as String)

            product = Product.findByNumber(data.productNumber as String) ?: createNewProduct(data)
            if (!product) return

            addDataToProduct(product, productObj)

            if (!product.htmlDescription && productObj.body) {
                product.htmlDescription = productObj.body
            }

            if (attachments) {
                attachmentService.populateAttachments(attachments, product)
            }

            switch (categoryService.categorySource) {
                case CategorySource.ITEM_CLASS:
                    categories = categoryService.getCategoriesFromProductAttributes(productObj.attributes as List)
                    subcategories = categoryService.getSubcategoriesFromProductAttributes(productObj.attributes as List)
                    sub2categories = categoryService.getSub2categoriesFromProductAttributes(productObj.attributes as List)
                    addCategoriesBasedOnItemClasses(product, categories, subcategories, sub2categories)
                    break
                case CategorySource.SALES_CATEGORY:
                    categories = productObj.salesCategories as List
                    addCategoriesBasedOnSalesCategories(product, categories)
                    break
            }

            if (attributeTerms) {
                attributeTerms.each { attributeTermObj ->
                    attribute = attributeService.getAttribute(attributeTermObj, product)
                    attributeTerm = attributeService.getAttributeTerm(attributeTermObj.value as String, attribute)
                    product.addToAttributes(attribute).save()
                    product.addToAttributeTerms(attributeTerm).save()
                }
            }

            if (tags) {
                tags.each { tagObj ->
                    tagName = tagObj.value
                    tagDescription = tagObj.description
                    tag = tagService.getTag(tagName, tagDescription)
                    product.addToTags(tag).save()
                }
            }

            if (descriptions) {
                descriptions.each { lang, description ->
                    product.setDescription(lang, description)
                }
//                product.save()
            }
        }

        Product.withSession { Session session ->
            session.flush()
            session.clear()
        }

        if (pageSize > 0 && products.size() > 0) {
            attachments = null
            categories = null
            subcategories = null
            sub2categories = null
            attributeTerms = null
            tags = null
            data = null
            descriptions = null
            products = null
            System.gc()
//            populateProducts(++pageNumber)
        }
    }

//    void addCategoriesBasedOnItemClasses(Product product, List categories, List subcategories, List sub2categories) {
//        if (!product) return
//
//        if (categories) {
//            Category category
//            String categoryName, categoryVismaId
//            categories.each { categoryObj ->
//                categoryName = categoryObj.description
//                categoryVismaId = categoryObj.value
//                if (categoryVismaId.length() == categoryService.CATEGORY_LENGTH) {
//                    category = categoryService.getCategoryByVismaId(categoryVismaId, categoryName)
//                    product.addToCategories(category).save()
//                } else if (categoryVismaId.contains(',')) {
//                    categoryVismaId.split(',').each {
//                        category = categoryService.getCategoryByVismaId(it.trim())
//                        product.addToCategories(category).save()
//                    }
//                }
//            }
//        }
//
//        if (subcategories) {
//            Category subcategory
//            String subcategoryName, subcategoryVismaId
//            subcategories.each { subcategoryObj ->
//                subcategoryName = subcategoryObj.description
//                subcategoryVismaId = subcategoryObj.value
//                if (subcategoryVismaId.length() == categoryService.SUBCATEGORY_LENGTH) {
//                    subcategory = categoryService.getSubcategoryByVismaId(subcategoryVismaId, subcategoryName)
//                    product.addToCategories(subcategory).save()
//                } else if (subcategoryVismaId.contains(',')) {
//                    subcategoryVismaId.split(',').each {
//                        subcategory = categoryService.getSubcategoryByVismaId(it.trim())
//                        product.addToCategories(subcategory).save()
//                    }
//                }
//            }
//        }
//
//        if (sub2categories) {
//            Category sub2category
//            String sub2categoryName, sub2categoryVismaId
//            sub2categories.each { sub2categoryObj ->
//                sub2categoryName = sub2categoryObj.description
//                sub2categoryVismaId = sub2categoryObj.value
//                if (sub2categoryVismaId.length() == categoryService.SUB2CATEGORY_LENGTH) {
//                    sub2category = categoryService.getSub2categoryByVismaId(sub2categoryVismaId, sub2categoryName)
//                    product.addToCategories(sub2category).save()
//                } else if (sub2categoryVismaId.contains(',')) {
//                    sub2categoryVismaId.split(',').each {
//                        sub2category = categoryService.getSub2categoryByVismaId(it.trim())
//                        product.addToCategories(sub2category).save()
//                    }
//                }
//            }
//        }
//    }

    /**
     * Adds categories based on item classes to a product
     * @param product as Product
     * @param categories as List (category objects from Visma)
     * @param subcategories as List (subcategory objects from Visma)
     * @param sub2categories as List (sub2category objects from Visma)
     */
    void addCategoriesBasedOnItemClasses(Product product, List categories, List subcategories, List sub2categories) {
        if (!product) return

        // Process main categories
        processCategoryList(product, categories, categoryService.CATEGORY_LENGTH) { vismaId, name ->
            categoryService.getCategoryByVismaId(vismaId, name)
        }

        // Process subcategories
        processCategoryList(product, subcategories, categoryService.SUBCATEGORY_LENGTH) { vismaId, name ->
            categoryService.getSubcategoryByVismaId(vismaId, name)
        }

        // Process sub2categories
        processCategoryList(product, sub2categories, categoryService.SUB2CATEGORY_LENGTH) { vismaId, name ->
            categoryService.getSub2categoryByVismaId(vismaId, name)
        }
    }

    /**
     * Helper method to process a list of categories and add them to a product
     * @param product The product to add categories to
     * @param categoryList List of category objects from Visma
     * @param expectedLength Expected length of category Visma ID
     * @param categoryResolver Closure that resolves a category by Visma ID and name
     */
    private void processCategoryList(Product product, List categoryList, int expectedLength, Closure categoryResolver) {
        if (!categoryList) return

        categoryList.each { categoryObj ->
            String categoryName = categoryObj.description
            String categoryVismaId = categoryObj.value

            if (categoryVismaId.length() == expectedLength) {
                Category category = categoryResolver(categoryVismaId, categoryName)
                if (category) {
                    product.addToCategories(category).save()
                } else {
                    log.error("Category with vismaId '${categoryVismaId}' was not added to '${product}'")
                }
            } else if (categoryVismaId.contains(',')) {
                categoryVismaId.split(',').each { String id ->
                    Category category = categoryResolver(id.trim(), null)
                    if (category) {
                        product.addToCategories(category).save()
                    } else {
                        log.error("Category with vismaId '${id.trim()}' was not added to '${product}'")
                    }
                }
            }
        }
    }

    void addCategoriesBasedOnSalesCategories(Product product, List productSalesCategories) {
        if (product && productSalesCategories) {
            Category category
            String categoryName
            String categoryVismaId
            productSalesCategories.each { productSalesCategoryObj ->
                categoryName = productSalesCategoryObj.description
                categoryVismaId = productSalesCategoryObj.categoryId as String
                if (categoryVismaId) {
                    category = categoryService.getCategoryByVismaId(categoryVismaId)
                    if (category) {
                        product.addToCategories(category).save()
                    } else {
                        log.error("Category with vismaId '${categoryVismaId}' was not added to '${product}'")
                    }
                }
            }
        }
    }

    /**
     * Getting a product from Visma and putting to the local DB
     * @param productNumber as String
     */
    void addNewProduct(String productNumber) {
        def productObj = vismaConnectService.getProduct(productNumber)
        if (!productObj) {
            println "Product hasn't been found"
        }

        addNewProduct(productObj)
    }

    /**
     * Add a new product by "Inventory" object from Visma
     * @param productVismaObj as Object
     * @return product as Product
     */
    Product addNewProduct(def productVismaObj) {
        Map data = [:]
        List attachments = productVismaObj.attachments ?: []
        List attributeTerms = attributeService.getAttributeTermsFromProductAttributes(productVismaObj.attributes as List)
        List tags = tagService.getTagsFromProductAttributes(productVismaObj.attributes as List)
        Map<Language, String> descriptions = attributeService.getDescriptionsByLangFromProductAttributes(productVismaObj.attributes as List)

        data.productNumber = productVismaObj.inventoryNumber
        data.vismaId = productVismaObj.inventoryId
        data.description = productVismaObj.description
        data.htmlDescription = productVismaObj.body
        data.productClass  = productVismaObj.itemClass?.description
        data.itemClassId = productVismaObj.itemClass?.id
        data.availability = getAvailability(productVismaObj.warehouseDetails as List)
        data.groupNumber = getGroupNumber(productVismaObj.attributes as List)
        data.status = getProductStatus(productVismaObj.status as String)
        data.type = getProductType(productVismaObj.type as String)

        Product product = Product.findByNumber(data.productNumber as String) ?: createNewProduct(data)
        if (!product) return null

        addDataToProduct(product, productVismaObj)

        if (attachments) {
            attachmentService.populateAttachments(attachments, product, true)
        }

        switch (categoryService.categorySource) {
            case CategorySource.ITEM_CLASS:
                List categories = categoryService.getCategoriesFromProductAttributes(productVismaObj.attributes as List)
                List subcategories = categoryService.getSubcategoriesFromProductAttributes(productVismaObj.attributes as List)
                List sub2categories = categoryService.getSub2categoriesFromProductAttributes(productVismaObj.attributes as List)
                addCategoriesBasedOnItemClasses(product, categories, subcategories, sub2categories)
                break
            case CategorySource.SALES_CATEGORY:
                List productSalesCategories = productVismaObj.salesCategories as List
                addCategoriesBasedOnSalesCategories(product, productSalesCategories)
                break
        }

        if (attributeTerms) {
            Attribute attribute
            AttributeTerm attributeTerm
            attributeTerms.each { attributeTermObj ->
                attribute = attributeService.getAttribute(attributeTermObj, product)
                if (!attribute) { return }
                attributeTerm = attributeService.getAttributeTerm(attributeTermObj.value as String, attribute)
                product.addToAttributes(attribute).save()
                product.addToAttributeTerms(attributeTerm).save()
            }
        }

        if (tags) {
            Tag tag
            tags.each { tagObj ->
                tag = tagService.getTag(tagObj.value as String, tagObj.description as String)
                product.addToTags(tag).save()
            }
        }

        if (descriptions) {
            descriptions.each { lang, description ->
                product.setDescription(lang, description)
            }
//            product.save()
        }

        Product.withSession { Session session ->
            session.flush()
        }

        return product
    }

    /**
     * Creating a new product
     * @param data as Map
     * @param flush as Boolean
     * @return product as Product
     */
    @Transactional
    Product createNewProduct(Map data, Boolean flush = false) {
        Product product = new Product(
                number: data.productNumber,
                description: data.description,
                htmlDescription: data.htmlDescription,
                productClass: data.productClass,
                itemClassId: data.itemClassId,
                availability: data.availability as Integer,
                groupNumber: data.groupNumber as Integer,
                vismaId: data.vismaId as Long,
                status: data.status as ProductStatus,
                type: data.type as ProductType
        )
        if (product.validate()) {
            if (product.save(flush: flush)) {
                log.info("Created new product: ${product}")
//                TODO: probably should be removed
//                if (appService.isMultilingual) {
//                    List<Tenant> tenants = tenantService.getAllNonMainTenants()
//                    tenants.each { Tenant tenant ->
//                        Object productObj = vismaConnectService.getProductByLang(product.number, tenant.lang)
//                        if (productObj) {
//                            product.setDescription(tenant.lang, productObj.description as String)
//                            product.setHtmlDescription(tenant.lang, productObj.htmlDescription as String)
//                        }
//                    }
//                }
                return product
            } else {
                log.error("Error creating new product: ${product}")
                if (appService.debugMode) {
                    product.errors.allErrors.each { println it }
                }
                return null
            }
        } else {
            log.error("Validation issue occurred while creating new product: ${product}")
            if (appService.debugMode) {
                product.errors.allErrors.eachWithIndex {error, idx ->
                    println "_error_${idx + 1}_ '$error'"
                }
            }
            return null
        }
    }

    /**
     * Calculates total product availability across all warehouses
     * @param warehouseDetails as List
     * @return Total availability as Integer (0 if no warehouses or empty list)
     */
    Integer getAvailability(List<Object> warehouseDetails) {
        if (!warehouseDetails) {
            return 0
        }

        return warehouseDetails.sum { warehouse ->
            (warehouse.available as Integer) ?: 0
        } ?: 0
    }

    /**
     * Getting product updates
     * @param lastModifiedDateTime as String
     * @param pageNumber (default 1)
     * @param pageSize (default 500)
     */
    @Transactional
    void checkForProductUpdates(UpdateType updateType, Integer pageNumber = 1, Integer pageSize = 500) {
        if (!vismaProductsPaging) {
            pageSize = 0
        }
        Map<String, ?> params = [:]
        switch (updateType) {
            case UpdateType.FULL:
                params.pageNumber = pageNumber
                params.pageSize = pageSize
                break
            case UpdateType.STOCK:
                params.availabilityLastModifiedDateTime = updateService.getLastModifiedDateTime()
                params.availabilityLastModifiedDateTimeCondition = '>'
                params.pageNumber = pageNumber
                params.pageSize = pageSize
                break
            case UpdateType.ATTACHMENT:
                params.attachmentLastModifiedDateTime = updateService.getLastModifiedDateTime()
                params.attachmentLastModifiedDateTimeCondition = '>'
                params.pageNumber = pageNumber
                params.pageSize = pageSize
                break
            default:
                params.lastModifiedDateTime = updateService.getLastModifiedDateTime()
                params.lastModifiedDateTimeCondition = '>'
                params.pageNumber = pageNumber
                params.pageSize = pageSize
        }

        List<Object> productsToCheck
        try {
            productsToCheck = vismaConnectService.getProducts(params) as List
        } catch (Exception e) {
            throw new ProductUpdateException('While getting products from Visma.NET')
        }

        if (!productsToCheck) {
            log.warn("No products to check for updates")
            return
        }

        Product product
        List<Object> newVismaProducts = []
        List<Product> productsToUpdate = []
        List<Product> products = Product.list()

        productsToCheck.each { productToCheck ->
            if (productToCheck.inventoryNumber) {
                product = products.find {
                    it.number == productToCheck.inventoryNumber as String
                }

                if (!product && isValidProductStatus(productToCheck.status as String)) {
                    newVismaProducts.push(productToCheck)
                } else if (!product && !isValidProductStatus(productToCheck.status as String)) {
                    log.warn("Product '${productToCheck.inventoryNumber}' has invalid status - '${productToCheck.status}'")
                } else if (product && productUpdated(updateType, product, productToCheck)) {
                    productsToUpdate.push(product)
                } else {
                    log.info("Product '$product' is relevant")
                }
            }
        }

        if (newVismaProducts) {
            List<Product> newProducts = []
            newVismaProducts.each { newProductVismaObj ->
                product = addNewProduct(newProductVismaObj)
                if (product) {
                    newProducts.push(product)
                }
            }
            if (newProducts) {
                sendProductsToWoo(newProducts)
            }
            newProducts = null
        }

        if (productsToUpdate) {
            updateProductsInWoo(productsToUpdate)
        }

        if (pageSize > 0 && productsToCheck.size() == pageSize) {
            params = null
            productsToCheck = null
            newVismaProducts = null
            productsToUpdate = null
            products = null
            System.gc()
            checkForProductUpdates(updateType, ++pageNumber)
        }
    }

    /**
     * Check a product for updates
     * @param updateType as UpdateType
     * @param product as Product
     * @param productVismaObj as Object
     * @return Boolean
     */
    @Transactional
    Boolean productUpdated(UpdateType updateType, Product product, def productVismaObj) {
        Boolean updated = false

        if (updateType == UpdateType.REGULAR || updateType == UpdateType.FULL) {
            // Check product fields
            updated = updateService.fieldUpdated(product, 'number', productVismaObj.inventoryNumber) || updated
            updated = updateService.fieldUpdated(product, 'description', productVismaObj.description) || updated
            updated = updateService.fieldUpdated(product, 'productClass', productVismaObj.itemClass?.description) || updated
            updated = updateService.fieldUpdated(product, 'itemClassId', productVismaObj.itemClass?.id) || updated
            updated = updateService.fieldUpdated(product, 'groupNumber', getGroupNumber(productVismaObj.attributes as List)) || updated
            updated = updateService.fieldUpdated(product, 'htmlDescription', productVismaObj.body) || updated
            updated = productStatusUpdated(product, productVismaObj.status as String) || updated
            updated = productTypeUpdated(product, productVismaObj.type as String) || updated

            // Check translations
            if (appService.isMultilingual) {
                updated = checkTranslationUpdates(product, productVismaObj.attributes as List) || updated
            }

            // Check categories
            List categories = categoryService.categorySource == CategorySource.ITEM_CLASS ?
                    categoryService.getCategoriesAndSubcategoriesFromProductAttributes(productVismaObj.attributes as List) :
                    productVismaObj.salesCategories as List
            updated = categoryService.categoriesUpdated(product, categories) || updated

            // Check attributes
            List attributeTerms = attributeService.getAttributeTermsFromProductAttributes(productVismaObj.attributes as List)
            updated = attributeService.attributesUpdated(product, attributeTerms) || updated

            // Check tags
            List tags = tagService.getTagsFromProductAttributes(productVismaObj.attributes as List)
            updated = tagService.tagsUpdated(product, tags) || updated

            // Check price class
            if (productVismaObj.priceClass) {
                try {
                    PriceClass priceClass = PriceClass.findByTypeAndVismaId(PriceClassType.PRODUCT, productVismaObj.priceClass.id as String) ?:
                            priceService.createNewPriceClass(PriceClassType.PRODUCT, productVismaObj.priceClass)
                    if (product.priceClass != priceClass) {
                        product.priceClass = priceClass
                        updated = true
                    }
                } catch(Exception ex) {
                    println ex.message
                }
            }
        }

        // Check stock updates
        if (updateType == UpdateType.STOCK || updateType == UpdateType.FULL) {
            updated = updateService.fieldUpdated(product, 'availability', getAvailability(productVismaObj.warehouseDetails as List)) || updated
        }

        // Check attachments
        if ((productVismaObj.attachments || product.attachments) && (updateType == UpdateType.ATTACHMENT || updateType == UpdateType.FULL)) {
            updated = attachmentService.attachmentsUpdated(product, productVismaObj.attachments as List) || updated
        }

        if (updated) {
            Product.withSession { Session session ->
                session.flush()
            }
            return true
        }
        return false
    }

    /**
     * Check for updates in product translations from product attributes
     * @param product as Product
     * @param productAttributes as List of product attributes containing translations
     * @return Boolean indicating if any translations were updated
     */
    private Boolean checkTranslationUpdates(Product product, List productAttributes) {
        if (!productAttributes) {
            return false
        }
        
        Boolean updated = false
        Map<Language, String> descriptions = attributeService.getDescriptionsByLangFromProductAttributes(productAttributes)
        
        descriptions.each { Language lang, String description ->
            ProductText productText = product.texts.find { it.lang == lang }
            if (!productText) {
                productText = new ProductText(lang: lang, product: product)
                product.addToTexts(productText)
                updated = true
            }
            
            updated = updateService.fieldUpdated(productText, 'description', description) || updated
        }
        
        return updated
    }

    /**
     * Sending products to WooCommerce
     * @param products as List (optional)
     * @param toUpdate as Boolean (optional)
     */
    void sendProductsToWoo(List<Product> products = [], Boolean toUpdate = false) {
        if (!products) {
            println '\r\n---> Sending unmapped products to the WooCommerce...'
            products = Product.createCriteria().list {
                texts {
                    isNull('wooId')
                }
                distinct('id')
            } as List<Product>

            if (!products) {
                log.warn("No unmapped products found")
                return
            }
        }

        products.each { Product product ->
            if (toUpdate) {
                try {
                    updateProductInWoo(product)
                } catch (Exception exc) {
                    log.error("Error updating product '${product}': ${exc.message}")
                }
            } else {
                try {
                    sendProductToWoo(product)
                } catch (Exception exc) {
                    log.error("Error sending product '${product}': ${exc.message}")
                }
            }
        }
        
        Product.withSession { Session session ->
            session.flush()
            session.clear()
        }

        log.info("Finished processing ${products.size()} products")
    }

    /**
     * Sending product to WooCommerce with WPML translation support
     * @param product as Product
     */
    void sendProductToWoo(Product product) {
        if(!product) {
            log.warn("No product to send to WooCommerce")
            return
        }

        Language defaultLang = tenantService.getMainTenantLanguage()
        ProductText defaultProductText = product.texts.find { it.lang == defaultLang }
        if (defaultProductText?.wooId) {
            if (appService.isMultilingual) {
                sendProductTranslationsToWoo(product)
            }
            return
        }

        // Send main product first
        if (!defaultProductText) {
            log.warn("No default ProductText found for product '${product}'. Cannot send to WooCommerce.")
            return
        }

        def data = wooConnectService.mapProductToWooApi(product, defaultLang)
        log.info("Sending product '${product}' to WooCommerce (${defaultLang.displayName})")
        def wooProductObj = wooConnectService.sendProductToWoo(data)
        if (!wooProductObj) {
            throw new Exception("Something went wrong while sending '$product' to Woo")
        }

        if (wooProductObj.images) {
            attachmentService.updateWooId(product, wooProductObj.images as List)
        }

        log.info("Product '$product' sent to WooCommerce (${defaultLang.displayName})")

        if (wooProductObj.id) {
            defaultProductText.wooId = wooProductObj.id as Long
        }

        product.save(flush: true)

        // If multilingual is enabled, send translations after the main product is created
        if (appService.isMultilingual) {
            sendProductTranslationsToWoo(product)
        }
    }

    /**
     * Updating product in WooCommerce
     * @param product as Product
     */
    void updateProductInWoo(Product product) throws Exception {
        if (!product) {
            log.warn("No product to update in WooCommerce")
            return
        }

        Language defaultLang = tenantService.getMainTenantLanguage()
        ProductText defaultProductText = product.texts.find { it.lang == defaultLang }
        if (!defaultProductText?.wooId) {
            log.warn("No default ProductText found for product '${product}'. Cannot update in WooCommerce.")
            return
        }

        def data = wooConnectService.mapProductToWooApi(product, defaultLang, true)
        log.info("Updating product '${product}' in WooCommerce (${defaultLang.displayName})")
        def wooProductObj = wooConnectService.updateProductInWoo(data, defaultProductText.wooId)
        if (!wooProductObj) { return }

        if (wooProductObj.images) {
            attachmentService.updateWooId(product, wooProductObj.images as List)
        }

        log.info("Product '$product' updated in WooCommerce (${defaultLang.displayName})")

        if (appService.isMultilingual) {
            updateProductTranslationsInWoo(product)
        }
    }

    /**
     * Update product translations in WooCommerce
     * @param product as Product
     */
    private void updateProductTranslationsInWoo(Product product) {
        Language defaultLang = tenantService.getMainTenantLanguage()
        List<Tenant> tenants = tenantService.getAllNonMainTenants()

        tenants.each { Tenant tenant ->
            // Skip Main tenant as it's already updated as the main product
            if (tenant.lang == defaultLang) {
                return
            }

            ProductText productText = product.texts.find { it.lang == tenant.lang }
            if (productText?.wooId) {
                def data = wooConnectService.mapProductToWooApi(product, tenant.lang, true)
                log.info("Updating product '${product}' translation in WooCommerce (${tenant.lang})")
                def wooProductObj = wooConnectService.updateProductInWoo(data, productText.wooId)
                if (wooProductObj) {
                    log.info("Product '$product' translation updated in WooCommerce (${tenant.lang})")
                }
            }
        }
    }

    /**
     * Send translations of a product to WooCommerce using WPML
     * @param product as Product whose translations should be sent
     */
    private void sendProductTranslationsToWoo(Product product) {
        // Check if default ProductText has a WooCommerce ID (main product)
        Language defaultLang = tenantService.getMainTenantLanguage()
        ProductText englishProductText = product.texts.find { it.lang == defaultLang }
        if (!englishProductText?.wooId) {
            log.warn("Cannot send translations for product '${product}' without default ProductText WooCommerce ID")
            return
        }

        List<Tenant> tenants = tenantService.getAllNonMainTenants()

        tenants.each { Tenant tenant ->
            if (tenant.lang == defaultLang) {
                return
            }

            ProductText productText = product.texts.find { it.lang == tenant.lang }

            if (!productText) {
                log.warn("No ProductText found for product '${product}' in language ${tenant.lang}")
                return
            }

            sendProductTranslationToWoo(product, tenant.lang)
        }
    }

    /**
     * Send a product translation to WooCommerce with WPML support
     * @param product as Product to send
     * @param lang as Language for the translation
     */
    private void sendProductTranslationToWoo(Product product, Language lang) {
        ProductText productText = product.texts.find { it.lang == lang }
        if (productText?.wooId) {
            log.warn("Translation for product '${product}' in language ${lang} already exists with WooCommerce ID: ${productText.wooId}")
            return
        }

        if (!productText) {
            log.warn("No ProductText found for product '${product}' in language ${lang}")
            return
        }

        def data = wooConnectService.mapProductToWooApi(product, lang)
        if (!data) {
            log.warn("Failed to map product '${product}' for WooCommerce API in language ${lang}")
            return
        }

        log.info("Sending product '${product}' translation to WooCommerce in language ${lang}")
        def wooProductObj = wooConnectService.sendProductToWoo(data)

        if (wooProductObj?.id) {
            productText.wooId = wooProductObj.id as Long
            product.save(flush: true)
            log.info("Updated translation wooId for product '${product}' in language ${lang}: ${productText.wooId}")
        } else {
            log.warn("Failed to send product '${product}' translation to WooCommerce in language ${lang}")
        }
    }

    /**
     * Get all products where vismaId=0 and fix it
     */
    void fixProductsVismaId() {
        List<Product> productsToFix = Product.findAllByVismaId(0)
        List<Object> vismaProducts = []
        try {
            vismaProducts = vismaConnectService.getProducts() as List
        } catch (Exception e) {
            errorHandlerService.logErrorDetails('PRODUCT-FIX-ERROR', 'While getting products from Visma.NET', e)
        }

        Object vismaProductObj
        if (!productsToFix || !vismaProducts) { return }
        productsToFix.each { product ->
            vismaProductObj = vismaProducts.find { it.inventoryNumber == product.number }
            if (vismaProductObj) {
                product.vismaId = vismaProductObj.inventoryId as Long
                product.save()
            }
        }
        Product.withSession { Session session ->
            session.flush()
            session.clear()
        }
    }

    @Transactional
    void fixProductsWooId() {
        // Find products that don't have any ProductText with wooId (completely unsynced)
        List<Product> productsWithoutWooId = Product.withCriteria {
            not {
                texts {
                    isNotNull('wooId')
                }
            }
        }
        if (!productsWithoutWooId) {
            log.info("No products without wooId")
            return
        }
        println "...found ${productsWithoutWooId.size()} to fix..."
        List<Object> wooProducts = wooConnectService.getProducts()?.collect {
            Map<String, ?> data = [:]
            data.productWooId = it.id
            data.productNumber = it.sku
            data
        }

        if (!wooProducts) { return }

        Language defaultLang = tenantService.getMainTenantLanguage()
        Product.withSession { Session session ->
            productsWithoutWooId.each { Product product ->
                def wooProduct = wooProducts.find { it.productNumber == product.number }
                if (wooProduct) {
                    // Find or create the default language ProductText
                    ProductText defaultProductText = product.texts.find { it.lang == defaultLang }
                    if (!defaultProductText) {
                        defaultProductText = new ProductText(lang: defaultLang, product: product)
                        product.addToTexts(defaultProductText)
                    }
                    defaultProductText.wooId = wooProduct.productWooId as Long
                    product.save()
                }
            }
            session.flush()
            session.clear()
        }
    }

    /**
     * Deleting invalid products
     */
    @Transactional
    void deleteInvalidProducts() {
        List<Long> vismaProducts = vismaConnectService.getProductIdsInBatches()
        List<Product> products = Product.list()
        List<Product> productsToDelete = []
        products.each { Product product ->
            if (!vismaProducts.contains(product.vismaId)) {
                productsToDelete << product
            }
        }

        if (productsToDelete) {
            log.info("Deleting ${productsToDelete.size()} invalid products")
            if (appService.debugMode) {
                println "\r\n---Products to delete---"
                productsToDelete.each {
                    println it
                }
                println "------------------------"
            }
            Product productToDelete
            // Note: By default it's limited to up to 100 objects to be created, updated or deleted.
            productsToDelete.collate(100).each { List<Product> productsToDeleteBatch ->
                // Collect all wooIds from all ProductText entities for deletion
                List<Long> wooIdsToDelete = []
                productsToDeleteBatch.each { Product product ->
                    product.texts.each { ProductText productText ->
                        if (productText.wooId) {
                            wooIdsToDelete << productText.wooId
                        }
                    }
                }

                if (wooIdsToDelete) {
                    def result = wooConnectService.batchUpdateProducts(null, null, wooIdsToDelete)
                    if (result && result.delete) {
                        result.delete.each { wooProductObj ->
                            // Find the product that contains this wooId in any of its ProductText entities
                            productToDelete = productsToDeleteBatch.find { product ->
                                product.texts.any { it.wooId == wooProductObj.id as Long }
                            }
                            if (productToDelete) {
                                try {
                                    productToDelete.delete()
                                    log.info("Deleted product '$productToDelete'")
                                } catch (Exception ex) {
                                    log.error("Failed to delete product '$productToDelete': ${ex.message}")
                                }
                            }
                        }
                    }
                } else {
                    // If no wooIds found, just delete the products locally
                    productsToDeleteBatch.each { Product product ->
                        try {
                            product.delete()
                            log.info("Deleted product '$product' (no WooCommerce ID found)")
                        } catch (Exception ex) {
                            log.error("Failed to delete product '$product': ${ex.message}")
                        }
                    }
                }
            }
            Product.withSession { Session session ->
                session.flush()
                session.clear()
            }
        } else {
            log.info("No invalid products found")
        }
    }

    /**
     * Updating products in Woo via batches with WPML support
     * @param products as List of Product
     * @param batchSize as Integer (default 100)
     * @param excludeRecentlyCreated as Boolean (default true) - exclude products with translations created in the last 5 minutes
     */
    @Transactional(readOnly = true)
    void updateProductsInWoo(List<Product> products = [], Integer batchSize = 100, Boolean excludeRecentlyCreated = true) {
        if (!products) {
            // Find products that have at least one ProductText with wooId
            products = Product.withCriteria {
                texts {
                    isNotNull('wooId')
                }
            } as List
        }

        if (!products) {
            log.warn("No products found to update in WooCommerce")
            return
        }

        // Filter out products that were recently created (to avoid race conditions)
        if (excludeRecentlyCreated) {
            Date fiveMinutesAgo = new Date(System.currentTimeMillis() - 5 * 60 * 1000)
            products = products.findAll { Product product ->
                // Check if any ProductText was created in the last 5 minutes
                boolean hasRecentTranslations = product.texts.any { ProductText text ->
                    text.dateCreated && text.dateCreated > fiveMinutesAgo
                }
                return !hasRecentTranslations
            }

            if (!products) {
                log.info("No products to update - all products have recently created translations")
                return
            }
        }

        Language defaultLang = tenantService.getMainTenantLanguage()
        List<Tenant> allTenants = tenantService.getAllTenants()

        // Process products in batches
        products.collate(batchSize).each { List<Product> productsBatch ->
            updateProductBatch(productsBatch, defaultLang, allTenants)
        }

        log.info("Product batch update completed")
    }

    /**
     * Update a batch of products with WPML support
     * @param productsBatch as List of Product
     * @param defaultLang as Language
     * @param allTenants as List of Tenant
     */
    private void updateProductBatch(List<Product> productsBatch, Language defaultLang, List<Tenant> allTenants) {
        Map<Language, List<ProductUpdateData>> updatesByLanguage = [:]
        Map<Language, List<ProductUpdateData>> newTranslationsByLanguage = [:]

        // Group updates and new translations by language
        productsBatch.each { Product product ->
            allTenants.each { Tenant tenant ->
                ProductText productText = product.texts.find { it.lang == tenant.lang }

                if (productText?.wooId) {
                    // Existing translation - add to updates
                    if (!updatesByLanguage[tenant.lang]) {
                        updatesByLanguage[tenant.lang] = []
                    }
                    updatesByLanguage[tenant.lang] << new ProductUpdateData(
                        product: product,
                        productText: productText,
                        language: tenant.lang
                    )
                } else if (productText && tenant.lang != defaultLang) {
                    // New translation (non-default language) - check if main product exists
                    ProductText defaultProductText = product.texts.find { it.lang == defaultLang }
                    if (defaultProductText?.wooId) {
                        if (!newTranslationsByLanguage[tenant.lang]) {
                            newTranslationsByLanguage[tenant.lang] = []
                        }
                        newTranslationsByLanguage[tenant.lang] << new ProductUpdateData(
                            product: product,
                            productText: productText,
                            language: tenant.lang
                        )
                    }
                }
            }
        }

        // Process updates for each language
        updatesByLanguage.each { Language lang, List<ProductUpdateData> updates ->
            processLanguageUpdates(updates, lang, true)
        }

        // Process new translations for each language
        newTranslationsByLanguage.each { Language lang, List<ProductUpdateData> newTranslations ->
            processLanguageUpdates(newTranslations, lang, false)
        }
    }

    /**
     * Process language-specific updates or new translations
     * @param updateDataList as List of ProductUpdateData
     * @param lang as Language
     * @param isUpdate as Boolean (true for updates, false for new translations)
     */
    private void processLanguageUpdates(List<ProductUpdateData> updateDataList, Language lang, Boolean isUpdate) {
        if (!updateDataList) {
            return
        }

        if (isUpdate) {
            // Process existing translations - use individual updates for better error handling
            updateDataList.each { ProductUpdateData updateData ->
                try {
                    def data = wooConnectService.mapProductToWooApi(updateData.product, lang, true)
                    if (data) {
                        log.info("Updating product '${updateData.product}' translation in WooCommerce (${lang})")
                        def wooProductObj = wooConnectService.updateProductInWoo(data, updateData.productText.wooId)
                        if (wooProductObj?.images) {
                            attachmentService.updateWooId(updateData.product, wooProductObj.images as List)
                        }
                        if (wooProductObj) {
                            log.info("Product '${updateData.product}' translation updated in WooCommerce (${lang})")
                        }
                    }
                } catch (Exception e) {
                    log.error("Error updating product '${updateData.product}' translation in language ${lang}: ${e.message}")
                }
            }
        } else {
            // Process new translations - create them as new products with WPML linking
            updateDataList.each { ProductUpdateData updateData ->
                try {
                    sendProductTranslationToWoo(updateData.product, lang)
                } catch (Exception e) {
                    log.error("Error creating new translation for product '${updateData.product}' in language ${lang}: ${e.message}")
                }
            }
        }
    }

    /**
     * Data class to hold product update information
     */
    private static class ProductUpdateData {
        Product product
        ProductText productText
        Language language
    }

    /**
     * Get a range of only 'Active' products
     * @param products: List<Object>
     * @return
     */
    def getActiveProducts(def products) {
        if (products.every { !it.status }) { return null }
        return products.findAll {it.status == ProductStatus.ACTIVE.vismaName}
    }

    /**
     * Get a range of only 'Valid' products
     * Products that can be populated
     * @param products as List of Visma product objects
     * @return
     */
    def getProductsWithValidStatus(def products) {
        if (products.every { !it.status }) { return null }
        return products.findAll {
            it.status != ProductStatus.INACTIVE.vismaName && it.status != ProductStatus.MARKED_FOR_DELETION.vismaName
        }
    }

    /**
     * Check if product can be populated
     * @param productStatus: String
     * @return Boolean
     */
    Boolean isValidProductStatus(String productStatus) {
        if (!productStatus) { return false }
        productStatus != ProductStatus.INACTIVE.vismaName && productStatus != ProductStatus.MARKED_FOR_DELETION.vismaName
    }

    /**
     * Check for product updates
     * @param product as Product
     * @param lastModifiedDateTime as String
     */
    @Transactional
    void checkForProductUpdates(Product product, String lastModifiedDateTime) throws Exception {
        Map params = [:]
        if (lastModifiedDateTime) {
            params.lastModifiedDateTime = lastModifiedDateTime
            params.lastModifiedDateTimeCondition = '>'
        }

        def productToCheck = vismaConnectService.getProduct(product.number, params)
        if (!productToCheck) {
            log.warn("Product '$product' not found in Visma")
            throw new ProductUpdateException(product.number, "Product '$product' NOT FOUND in Visma")
        }

        if (productUpdated(UpdateType.FULL, product, productToCheck)) {
            updateProductInWoo(product)
        } else {
            log.info("Product '$product' is relevant")
        }
    }

    /**
     * Check if the product is visible
     * @param product as Product
     * @return bool
     */
    @Transactional
    Boolean isVisible(Product product) {
        if (product.visible) { return true }
        if (!product.groupNumber) { return false }
        List<Product> products = Product.findAllByGroupNumber(product.groupNumber)
        if (products.any { it.visible }) {
            return false
        } else {
            product.visible = true
            product.save(flush: true)
            return true
        }
    }

    /**
     * Get group number from product attributes
     * @param productAttributes as List of Object
     * @return
     */
    Integer getGroupNumber(List productAttributes) {
        if (!productAttributes) { return null }
        productAttributes.find { it.id == VARIATION_ATTRIBUTE_ID && it.value.isInteger() }?.value as Integer
    }

    /**
     * Adding data to product
     * @param product: Product
     * @param productVismaObj: Object
     */
    void addDataToProduct(Product product, Object productVismaObj) {
        if (productVismaObj.priceClass) {
            try {
                PriceClass priceClass = PriceClass.findByTypeAndVismaId(PriceClassType.PRODUCT, productVismaObj.priceClass?.id as String) ?:
                        priceService.createNewPriceClass(PriceClassType.PRODUCT, productVismaObj.priceClass)
                if (priceClass) priceClass.addToProducts(product)
            } catch (Exception ex) {
                println ex.message
            }
        }
    }

    /**
     * Get ProductStatus
     * @param vismaInventoryStatus: String
     * @return
     */
    ProductStatus getProductStatus(String vismaInventoryStatus) {
        switch (vismaInventoryStatus) {
            case ProductStatus.ACTIVE.vismaName:
                return ProductStatus.ACTIVE
            case ProductStatus.NO_SALES.vismaName:
                return ProductStatus.NO_SALES
            case ProductStatus.NO_PURCHASES.vismaName:
                return ProductStatus.NO_PURCHASES
            case ProductStatus.NO_REQUEST.vismaName:
                return ProductStatus.NO_REQUEST
            case ProductStatus.INACTIVE.vismaName:
                return ProductStatus.INACTIVE
            case ProductStatus.MARKED_FOR_DELETION.vismaName:
                return ProductStatus.MARKED_FOR_DELETION
            default:
                return ProductStatus.UNDEFINED
        }
    }

    /**
     * Check if product status updated
     * @param product: Product
     * @param currentStatus: String
     * @return Boolean
     */
    @Transactional
    Boolean productStatusUpdated(Product product, String currentStatus) {
        if (product.status != getProductStatus(currentStatus)) {
            product.status = getProductStatus(currentStatus)
            return true
        }
        return false
    }

    /**
     * Get ProductType
     * @param vismaInventoryType: String
     * @return
     */
    ProductType getProductType(String vismaInventoryType) {
        switch (vismaInventoryType) {
            case ProductType.FINISHED_GOOD_ITEM.vismaName:
                return ProductType.FINISHED_GOOD_ITEM
            case ProductType.NON_STOCK_ITEM.vismaName:
                return ProductType.NON_STOCK_ITEM
            default:
                return ProductType.UNDEFINED
        }
    }

    /**
     * Check if product type updated
     * @param product: Product
     * @param currentType: String
     * @return Boolean
     */
    @Transactional
    Boolean productTypeUpdated(Product product, String currentType) {
        if (product.type != getProductType(currentType)) {
            product.type = getProductType(currentType)
            return true
        }
        return false
    }

    /**
     * Get product labels
     * @param product: Product
     * @return labels: List<String>
     */
    List<String> getProductLabels(Product product) {
        List productLabels = []
        switch (product.status) {
            case ProductStatus.NO_PURCHASES:
                productLabels.push('Phaseout')
                break
            case ProductStatus.NO_SALES:
                productLabels.push('Replaced by')
                break
        }

        productLabels
    }

    @Transactional(readOnly = true)
    void checkForUnsyncedProducts() {
        // Find products that don't have any ProductText with wooId (completely unsynced)
        List<Product> products = Product.withCriteria {
            not {
                texts {
                    isNotNull('wooId')
                }
            }
        }
        if (products) {
            try {
                mailService.sendMail {
                    from "${optionService.companyName} <<EMAIL>>"
                    to optionService.emailSupport
                    subject "${optionService.companyName} | ${Environment.current.name.toUpperCase()} | Unsynced products"
                    text products.join("\n")
                }
            } catch (Exception ex) {
                println "MAIL-ERROR: [$appService.timeStamp] ${ex.message}"
                if (appService.debugMode) {
                    ex.printStackTrace()
                }
                throw ex
            }
        }
    }

}