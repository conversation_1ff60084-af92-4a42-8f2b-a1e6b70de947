package se.visionmate.vcommerce.core

import grails.gorm.transactions.Transactional
import grails.util.Environment
import org.hibernate.Session
import se.visionmate.vcommerce.api.VismaConnectService
import se.visionmate.vcommerce.api.WooConnectService
import se.visionmate.vcommerce.enums.CategoryType
import se.visionmate.vcommerce.enums.Language

class AttributeService {

    static String ITEM_CLASS_ID, ITEM_CLASS_TYPE, ITEM_CLASS_DESCRIPTION, VARIATION_ATTRIBUTE_ID

    AppService appService
    OptionService optionService
    VismaConnectService vismaConnectService
    WooConnectService wooConnectService

    void initialize() {
        Map<String, String> options = optionService.vismaOptions
        ITEM_CLASS_ID = options.visma_default_item_class_id
        ITEM_CLASS_TYPE = options.visma_default_item_class_type
        ITEM_CLASS_DESCRIPTION = options.visma_default_item_class_description
        VARIATION_ATTRIBUTE_ID = options.visma_variation_attribute_id
    }

    /**
     * Getting all attributes from Visma and putting to the local DB
     */
    void populateAttributes() {

        def itemClass = vismaConnectService.getItemClassById(ITEM_CLASS_ID as int)
        if (!itemClass) {
            println "No ItemClasses were found in Visma"
            return
        }

        def attributes = getAttributesFromItemClass(itemClass)

        if (!attributes) {
            println "No attributes were found"
        } else {
            String attributeName, attributeVismaId
            Integer attributePosition
            attributes.each { attr ->
                attributeName = attr.description
                attributeVismaId = attr.attributeId
                attributePosition = attr.sortOrder as Integer
                Attribute.findByVismaId(attributeVismaId) ?: createNewAttribute(attributeName, attributeVismaId, attributePosition)
            }
            Attribute.withSession { Session session ->
                session.flush()
                session.clear()
            }
        }
    }

    /**
     * Creating a new attribute
     * @param name as String
     * @param vismaId as String
     * @param flush Bool(optional)
     * @return new Attribute
     */
    @Transactional
    Attribute createNewAttribute(String name, String vismaId, Integer position, Boolean flush = false) {
        Attribute attribute = Attribute.findByName(name) ?
                new Attribute(name: changeAttributeName(name), vismaId: vismaId, position: position, variation: vismaId == VARIATION_ATTRIBUTE_ID) :
                new Attribute(name: name, vismaId: vismaId, position: position, variation: vismaId == VARIATION_ATTRIBUTE_ID)
        if (attribute.validate()) {
            if (attribute.save(flush: flush)) {
                println "[${new Date().format('yyyy-MM-dd HH:mm:ss')}] $attribute - attribute has been created"
                return attribute
            } else {
                println 'An error occurred while creating a new attribute'
                if (appService.debugMode) {
                    attribute.errors.allErrors.each { println it }
                }
                return null
            }
        } else {
            println 'Validation issue occurred while creating a new attribute'
            if (appService.debugMode) {
                attribute.errors.allErrors.each { println it }
            }
            return null
        }
    }

    /**
     * Changing attribute name to unique one
     * Sometimes we have two or more attributes with the same name that are not accepted by Woo
     * @param name as String
     * @param changedName as String
     */
    String changeAttributeName(String name) {
        Byte index = 1
        switch (name) {
            case ~/^.*-\d$/:
                index += name.find(/\d$/).toInteger()
                return name.replaceFirst(/\d$/, index as String)
                break
            case ~/^.*-\d\d$/:
                index += name.find(/\d\d$/).toInteger()
                return name.replaceFirst(/\d\d$/, index as String)
                break
            default:
                return "${name}-1"
        }
    }

    /**
     * Creating a new attribute term
     * @param name as String
     * @param attribute as Attribute
     * @param flush Bool(optional)
     * @return new AttributeTerm
     */
    @Transactional
    AttributeTerm createNewAttributeTerm(String name, Attribute attribute, Boolean flush = false) {
        AttributeTerm attributeTerm = new AttributeTerm(name: name, attribute: attribute)
        if (attributeTerm.validate()) {
            if (attributeTerm.save(flush: flush)) {
                println "[${new Date().format('yyyy-MM-dd HH:mm:ss')}] $attributeTerm - attribute term has been created"
                return attributeTerm
            } else {
                println 'An error occurred while creating a new attributeTerm'
                if (appService.debugMode) {
                    attributeTerm.errors.allErrors.each { println it }
                }
                return null
            }
        } else {
            println 'Validation issue occurred while creating a new attributeTerm'
            if (appService.debugMode) {
                attributeTerm.errors.allErrors.each { println it }
            }
            return null
        }
    }

    /**
     * Gets attributes from an ItemClass, filtering out category and language attributes
     * @param itemClass as Object containing attributes
     * @return List of filtered attributes or null
     */
    List getAttributesFromItemClass(def itemClass) {
        if (!itemClass) return null

        List<String> categoryAttributeIds = CategoryType.values().collect { it.attributeId }
        List<String> languageAttributeIds = Language.values().collect { it.attributeId }
        
        return itemClass.attributes.findAll { attribute ->
            !(attribute.attributeId in categoryAttributeIds) && 
            !(attribute.attributeId in languageAttributeIds)
        }
    }

    /**
     * Getting an attribute from itemClasses by attributeId(vismaId)
     * @param attributeVismaId as String
     * @return attributeObj as Object
     */
    def getAttributeFromItemClassesByVismaId(String attributeVismaId, Product product) {
        def itemClasses = vismaConnectService.getItemClasses()
        if (!itemClasses) {
            println "No ItemClasses were found in Visma"
            return
        }

        return itemClasses
                .find { it.id == product.itemClassId }.attributes
                .find { it.attributeId == attributeVismaId }
    }

    /**
     * Gets attribute terms from product attributes, filtering out category and language attributes
     * @param productAttributes as List of product attribute objects
     * @return Filtered list of attribute terms (never null)
     */
    List getAttributeTermsFromProductAttributes(List productAttributes) {
        if (!productAttributes) {
            return []
        }

        List<String> categoryAttributeIds = CategoryType.values().collect { it.attributeId }
        List<String> languageAttributeIds = Language.values().collect { it.attributeId }
        
        return productAttributes.findAll { attribute ->
            !(attribute.id in categoryAttributeIds) && 
            !(attribute.id in languageAttributeIds)
        }
    }

    /**
     * Gets descriptions by language from product attributes
     * @param productAttributes as List of product attribute objects
     * @return Map of language to description
     */
    Map<Language, String> getDescriptionsByLangFromProductAttributes(List productAttributes) {
        if (!productAttributes) {
            return [:]
        }

        Map<String, Language> languageByAttributeId = Language.values().collectEntries { 
            [(it.attributeId): it] 
        }

        return productAttributes.findResults { attribute ->
            Language lang = languageByAttributeId[attribute.id]
            lang ? [(lang): attribute.value] : null
        }.collectEntries()
    }

    /**
     * Getting an attribute by vismaId or create a new one
     * @param attributeTermObj: Object
     * @param forUpdate: Boolean
     * @return
     */
    Attribute getAttribute(def attributeTermObj, Product product, Boolean forUpdate = false) {
        String attributeTermVismaId = attributeTermObj.id
        Attribute attribute = Attribute.findByVismaId(attributeTermVismaId.trim())
        if (!attribute) {
            def attributeObj = getAttributeFromItemClassesByVismaId(attributeTermVismaId, product)
            if (attributeObj) {
                String attributeName = attributeObj.description
                String attributeVismaId = attributeObj.attributeId
                Integer attributePosition = attributeObj.sortOrder as Integer
                attribute = createNewAttribute(attributeName, attributeVismaId, attributePosition)
                if (forUpdate) sendAttributeToWoo(attribute)
            }
        } else if (!attribute.wooId) {
            if (forUpdate) sendAttributeToWoo(attribute)
        }
        attribute?.refresh()
    }

    /**
     * Getting an attributeTerm by name and attribute or create a new one
     * @param attributeTermName: String
     * @param attribute: String
     * @param forUpdate: Boolean
     * @return
     */
    AttributeTerm getAttributeTerm(String attributeTermName, Attribute attribute, Boolean forUpdate = false) {
        AttributeTerm attributeTerm = AttributeTerm.findByNameAndAttribute(attributeTermName.trim(), attribute)
        if (!attributeTerm) {
            attributeTerm = createNewAttributeTerm(attributeTermName, attribute)
            if (forUpdate) sendAttributeTermToWoo(attributeTerm, attribute.wooId)
        } else if (!attributeTerm.wooId) {
            if (forUpdate) sendAttributeTermToWoo(attributeTerm, attribute.wooId)
        }
        return attributeTerm?.refresh()
    }

    /**
     * Sending unmapped(without wooId) attributes to WooCommerce
     */
    void sendAttributesToWoo() {
        println '\r\nSending unmapped attributes to the WooCommerce'
        List attributes = Attribute.list().findAll { Attribute attribute ->
            attribute.wooId == null && attribute.toSync
        }

        if (!attributes) {
            println 'No unmapped attributes found'
            return
        }

        def data
        def wooAttributeObj
        def wooAttributeTermObj

        attributes.each { Attribute attr ->
            data = wooConnectService.mapAttributeToWooApi(attr)
            println "Sending attribute '${attr}' to the WooCommerce"
            wooAttributeObj = wooConnectService.sendAttributeToWoo(data)
            if (!wooAttributeObj) { return }

            attr.wooId = wooAttributeObj.id ?: null
            attr.save(flush: true)
            if (attr.attributeTerms && wooAttributeObj.id) {
                attr.attributeTerms.findAll { it.wooId == null }.each { AttributeTerm attrTerm ->
                    data = wooConnectService.mapAttributeToWooApi(attrTerm)
                    println "Sending attribute term '${attrTerm}' to the WooCommerce"
                    wooAttributeTermObj = wooConnectService.sendAttributeTermToWoo(data, wooAttributeObj.id as Integer)
                    if (wooAttributeTermObj) {
                        attrTerm.wooId = wooAttributeTermObj.id ?: null
                        attrTerm.save(flush: true)
                    }
                }
            }
        }
    }

    /**
     * Sending attribute to WooCommerce
     * @param attribute: Attribute
     */
    void sendAttributeToWoo(Attribute attribute) {
        if(!attribute || !attribute.toSync) { return }

        Map data = wooConnectService.mapAttributeToWooApi(attribute)
        println "\r\nSending attribute '${attribute}' to WooCommerce"
        Object wooAttributeObj = wooConnectService.sendAttributeToWoo(data)
        if (!wooAttributeObj) { return }

        println "Attribute '$attribute' sent to WooCommerce"
        attribute.wooId = wooAttributeObj.id ?: null
        attribute.save(flush: true)
    }

    /**
     * Sending attribute term to WooCommerce
     * @param attributeTerm: AttributeTerm
     * @param attributeId: Integer
     */
    void sendAttributeTermToWoo(AttributeTerm attributeTerm, Integer attributeId) {
        if(!attributeTerm || !attributeId || !attributeTerm.attribute.toSync) { return }

        Map data = wooConnectService.mapAttributeToWooApi(attributeTerm)
        println "Sending attribute term '${attributeTerm}' to WooCommerce"
        Object wooAttributeTermObj = wooConnectService.sendAttributeTermToWoo(data, attributeId)
        if (!wooAttributeTermObj) { return }

        println "Attribute term '$attributeTerm' sent to WooCommerce"
        attributeTerm.wooId = wooAttributeTermObj.id ?: null
        attributeTerm.save(flush: true)
    }

    /**
     * Updating attribute in WooCommerce
     * @param attribute as Attribute
     */
    void updateAttributeInWoo(Attribute attribute) {
        if(!attribute.wooId || !attribute.toSync) {
            println "There is no attribute to update in Woo"
            return
        }

        def data
        def wooAttributeObj

        data = wooConnectService.mapAttributeToWooApi(attribute, true)
        println "\r\nUpdating attribute '${attribute}' in WooCommerce"
        wooAttributeObj = wooConnectService.updateAttributeInWoo(data, attribute.wooId)
        if (!wooAttributeObj) { return }
        println "Attribute '$attribute' have been updated in WooCommerce"
    }

    /**
     * Checking attributes for updates
     * @param product as Product
     * @param attributes as List
     * @return updated as Boolean
     */
    @Transactional
    Boolean attributesUpdated(Product product, List attributeTerms) {
        Boolean updated = false
        Attribute attribute
        AttributeTerm attributeTerm
        attributeTerms.each { attributeTermObj ->
            attributeTerm = product.attributeTerms.find {it.attribute.vismaId == attributeTermObj.id?.trim() && it.name == attributeTermObj.value?.trim()}
            if (!attributeTerm) {
                attribute = getAttribute(attributeTermObj, product, true)
                if (!attribute) { return }

                attributeTerm = getAttributeTerm(attributeTermObj.value as String, attribute, true)
                product.attributes.find {it.id == attribute.id} ?: product.addToAttributes(attribute).save(flush: true)
                product.attributeTerms.find {it.attribute.id == attribute.id && it.name == attributeTerm.name} ?:
                        product.addToAttributeTerms(attributeTerm).save(flush: true)
                updated = true
            }
        }
        if (product.attributeTerms.size() != attributeTerms.size()) {
            product.attributeTerms.each { AttributeTerm prodAttrTerm ->
                if (!attributeTerms.find {it.id == prodAttrTerm.attribute.vismaId && it.value == prodAttrTerm.name}) {
                    product.refresh()
                    product.removeFromAttributeTerms(prodAttrTerm)
                    product.save(flush: true)
                    updated = true
                }
            }
        }
        return updated
    }

}