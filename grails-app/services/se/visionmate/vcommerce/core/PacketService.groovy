package se.visionmate.vcommerce.core

import grails.gorm.transactions.Transactional
import org.hibernate.Session
import se.visionmate.vcommerce.enums.PacketOption

@Transactional(readOnly = true)
class PacketService {

    AppService appService

    static Set<PacketOption> packetOptions = null

    void initialize() {
        packetOptions = initPacketOptions()
        log.info("PacketService initialized successfully")
    }

    Set<PacketOption> initPacketOptions() {
        List<Packet> packets = Packet.findAllByActive(true)
        if (!packets) {
            log.error("No active packet found. Stopping the application ...")
            System.exit(0)
        } else if (packets.size() > 1) {
            log.error("More than 1 packet active. Stopping the application ...")
            System.exit(0)
        }
        packets[0].options
    }

    Boolean isActivePacketOption(PacketOption packetOption) {
        if (!packetOptions) { initialize() }
        Boolean result = packetOptions.contains(packetOption)
        if (appService.debugMode) {
            log.info("Packet option '$packetOption' is ${result ? 'Active' : 'Inactive'}")
        }
        result
    }

    @Transactional
    Boolean activatePacket(Packet packet) {
        Boolean activated = false
        Set<Packet> activePackets = Packet.findAllByActive(true)
        Packet.withSession { Session session ->
            if (!activePackets) {
                packet.active = true
                activated = true
            } else if (activePackets.size() == 1) {
                activePackets[0].active = false
                packet.active = true
                activated = true
            } else if (activePackets.size() > 1) {
                activePackets.each { Packet activePacket ->
                    activePacket.active = false
                }
                packet.active = true
                activated = true
            } else {
                activated = false
            }
            session.flush()
        }
        initialize()
        activated
    }
}
