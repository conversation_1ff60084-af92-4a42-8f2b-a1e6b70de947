package se.visionmate.vcommerce.core

import grails.gorm.transactions.Transactional
import grails.util.Environment
import org.hibernate.Session
import se.visionmate.vcommerce.api.VismaConnectService
import se.visionmate.vcommerce.api.WooConnectService
import se.visionmate.vcommerce.enums.BatchUpdateType

class TagService {

    static String ITEM_CLASS_ID, ITEM_CLASS_TYPE, ITEM_CLASS_DESCRIPTION, VARIATION_ATTRIBUTE_ID

    AppService appService
    OptionService optionService
    VismaConnectService vismaConnectService
    WooConnectService wooConnectService

    void initialize() {
        Map<String, String> options = optionService.vismaOptions
        ITEM_CLASS_ID = options.visma_default_item_class_id
        ITEM_CLASS_TYPE = options.visma_default_item_class_type
        ITEM_CLASS_DESCRIPTION = options.visma_default_item_class_description
        VARIATION_ATTRIBUTE_ID = options.visma_variation_attribute_id
    }

    /**
     * Getting all tags from Visma and putting to the local DB
     */
    void populateTags() {

        def itemClasses = vismaConnectService.getItemClasses()
        if (!itemClasses) {
            println "No ItemClasses were found in Visma"
            return
        }

        def tags = getTagsFromItemClasses(itemClasses as List)

        if (!tags) {
            println "No tags were found"
        } else {
            String tagName, tagDescription
            tags.each { tag ->
                tagName = tag.id
                tagDescription = tag.description
                Tag.findByName(tagName) ?: createNewTag(tagName, tagDescription)
            }
            Tag.withSession { Session session ->
                session.flush()
                session.clear()
            }
        }
    }

    /**
     * Getting tags from itemClasses
     * @param itemClasses as List of Object
     * @return tags as List of Object
     */
    List getTagsFromItemClasses(List itemClasses){
        if (itemClasses) {
            return itemClasses
                    .find { it.type == ITEM_CLASS_TYPE && it.description == ITEM_CLASS_DESCRIPTION }.attributes
                    .find { it.attributeId == VARIATION_ATTRIBUTE_ID }.details
        }
        return []
    }

    /**
     * Creating a new tag
     * @param name as String
     * @param description as String
     * @param flush Bool(optional)
     * @return new Tag
     */
    @Transactional
    Tag createNewTag(String name, String description, Boolean flush = false) {
        Tag tag = Tag.findByName(name) ?: new Tag(name: name, description: description)
        if (tag.validate()) {
            if (tag.save(flush: flush)) {
                println "[${new Date().format('yyyy-MM-dd HH:mm:ss')}] $tag - tag has been created"
                return tag
            } else {
                println 'An error occurred while creating a new tag'
                if (appService.debugMode) {
                    tag.errors.allErrors.each { println it }
                }
                return null
            }
        } else {
            println 'Validation issue occurred while creating a new tag'
            if (appService.debugMode) {
                tag.errors.allErrors.each { println it }
            }
            return null
        }
    }

    /**
     * Getting tags from product attributes
     * @param productAttributes as List of Object
     * @return tags as List of Object
     */
    List getTagsFromProductAttributes(List productAttributes){
        if (productAttributes) {
            return productAttributes.findAll { it.id == VARIATION_ATTRIBUTE_ID  }
        }
        return []
    }

    /**
     * Getting a tag by name or create a new one
     * @param tagName: String
     * @param tagDescription: String
     * @param toUpdate: String
     * @return
     */
    Tag getTag(String tagName, String tagDescription, Boolean toUpdate = false) {
        Tag tag = Tag.findByName(tagName)
        if (!tag && tagName) {
            tag = createNewTag(tagName, tagDescription)
        }
        if (!tag.wooId && toUpdate) {
            tag.wooId = sendTagToWoo(tag)
        }
        return tag
    }

    /**
     * Sending tags to WooCommerce
     */
    void sendTagsToWoo() {
        println '\r\nSending unmapped tags to the WooCommerce'
        List tags = Tag.list().findAll { it.wooId == null }
        if (!tags) {
            println 'No unmapped tags found'
            return
        }

        def data
        def wooTagObj

        tags.each { Tag tag ->
            data = wooConnectService.mapTagToWooApi(tag)
            println "Sending tag '${tag}' to the WooCommerce"
            wooTagObj = wooConnectService.sendTagToWoo(data)
            if (!wooTagObj) { return }

            tag.wooId = wooTagObj.id ?: null
            tag.save(flush: true)
        }
    }

    /**
     * Send tag to WooCommerce
     * @param tag as Tag
     * @return wooId as Long
     */
    Long sendTagToWoo(Tag tag) {
        if (tag.wooId) {
            println "Tag '${tag}' is already synced"
            return tag.wooId
        }

        def data = wooConnectService.mapTagToWooApi(tag)
        println "Sending tag '${tag}' to the WooCommerce"
        def wooTagObj = wooConnectService.sendTagToWoo(data)
        tag.wooId = wooTagObj.id ?: null
        tag.save(flush: true)
        tag.wooId
    }

    /**
     * Updating tags in WooCommerce
     */
    void updateTagsInWoo() {
        println '\r\nUpdating tags in the WooCommerce'
        List<Tag> tags = Tag.list().findAll { it.wooId != null }
        if (!tags) {
            println 'No tags found'
            return
        }

        List tagsToUpdate = []
        tags.collate(100).each { List<Tag> tagsBatch ->
            tagsBatch.each { Tag tag ->
                tagsToUpdate << wooConnectService.mapTagToWooApi(tag, true)
            }
            if (tagsToUpdate) {
                wooConnectService.batchUpdate(BatchUpdateType.TAG, null, tagsToUpdate, null)
                tagsToUpdate = []
            }
        }
    }

    void deleteAllUnmappedTags() {
        println "\r\n[${new Date().format('yyyy-MM-dd HH:mm')}] Deleting all unmapped tags..."
        List<Long> tagsToDelete = []
        List<Long> wooTagIds = wooConnectService.getTagIdsInBatches()
        List<Long> tagWooIds = Tag.list().findAll { it.wooId != null }.collect { it.wooId }
        wooTagIds.each { Long wooTagId ->
            if (!tagWooIds.contains(wooTagId)) {
                tagsToDelete.push(wooTagId)
            }
        }
        if (tagsToDelete) {
            println "\r\nTags to delete: ${tagsToDelete.size()}"
            tagsToDelete.collate(100).each {List<Long> batchIds ->
                wooConnectService.batchUpdate(BatchUpdateType.TAG, null, null, batchIds)
            }
        }
    }

    /**
     * Check if the list of tags in the product is up to date
     * @param product: Product
     * @param tags: List<Object>
     * @return Boolean
     */
    Boolean tagsUpdated(Product product, List<Object> tags) {
        Boolean updated = false
        Tag tag
        tags.each { Object tagVismaObj ->
            tag = product.tags.find {it.name == tagVismaObj.value }
            if (!tag || !tag.wooId) {
                tag = getTag(tagVismaObj.value as String, null ,true)
                product.addToTags(tag).save(flush: true)
                updated = true
            }
        }
        if (product.tags.size() != tags.size()) {
            product.tags.each { Tag tagToCheck ->
                if (!tags.find { it.value == tagToCheck.name }) {
                    product.refresh()
                    product.removeFromTags(tagToCheck)
                    product.save(flush: true)
                    updated = true
                }
            }
        }
        updated
    }

}
