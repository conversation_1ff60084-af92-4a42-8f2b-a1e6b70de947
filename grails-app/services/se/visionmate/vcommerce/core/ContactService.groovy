package se.visionmate.vcommerce.core

import grails.gorm.transactions.Transactional
import grails.util.Environment
import se.visionmate.vcommerce.enums.ContactType

class ContactService {

    AppService appService
    UpdateService updateService

    /**
     * Creating a new contact
     * @param type as ContactType
     * @param data as Object
     * @param entity as Object
     * @param flush as <PERSON>olean(optional)
     */
    @Transactional
    void createNewContact(ContactType type, def entity, def data, Boolean flush = false) {
        Contact contact = new Contact(
                type: type,
                firstName: getCustomerContactFirstName(data.attention as String) ?: data.firstName,
                lastName: getCustomerContactLastName(data.attention as String) ?: data.lastName,
                email: data.email,
                phone1: data.phone1,
                phone2: data.phone2
        )
        switch (entity) {
            case Customer:
                contact.customer = entity
                break
            case Location:
                contact.location = entity
                break
        }
        if (contact.validate()) {
            if (contact.save(flush: flush)) {
                println "[${new Date().format('yyyy-MM-dd HH:mm:ss')}] $contact - has been created"
            } else {
                println 'An error occurred while creating a new contact'
                if (appService.debugMode) {
                    contact.errors.allErrors.each { println it }
                }
            }
        } else {
            println 'Validation issue occurred while creating a new contact'
            if (appService.debugMode) {
                contact.errors.allErrors.each { println it }
            }
        }
    }

    /**
     * Checking for a new contact
     * If doesnt exist create a new one
     * @param type as ContactType
     * @param entity as Object
     */
    void checkForNewContact(ContactType type, def entity, def data) {
        switch (entity) {
            case Customer:
                if (!Contact.findByTypeAndCustomer(type, entity)) {
                    createNewContact(type, entity, data)
                }
                break
            case Location:
                if (!Contact.findByTypeAndLocation(type, entity)) {
                    createNewContact(type, entity, data)
                }
                break
        }
    }

    /**
     * Check for contact updates
     * @param contact as Contact
     * @param contactVismaObj as Object
     * @return bool
     */
    Boolean contactUpdated(Contact contact, def contactVismaObj) {
        Boolean updated = false
        updated = updateService.fieldUpdated(
                contact,
                'firstName',
                contactVismaObj?.firstName ?: getCustomerContactFirstName(contactVismaObj?.attention as String)
        ) || updated
        updated = updateService.fieldUpdated(
                contact,
                'lastName',
                contactVismaObj?.lastName ?: getCustomerContactLastName(contactVismaObj?.attention as String)
        ) || updated
        updated = updateService.fieldUpdated(contact, 'email', contactVismaObj?.email) || updated
        updated = updateService.fieldUpdated(contact, 'phone1', contactVismaObj?.phone1) || updated
        updated = updateService.fieldUpdated(contact, 'phone2', contactVismaObj?.phone2) || updated
        updated
    }

    /**
     * Get firstName from fullName
     * @param fullName as String
     * @return String
     */
    String getCustomerContactFirstName(String fullName) {
        if (!fullName) { return null }
        fullName.split(/[\s;,]/)[0]
    }

    /**
     * Get lastName from fullName
     * @param fullName as String
     * @return String
     */
    String getCustomerContactLastName(String fullName) {
        if (!fullName) { return null }
        if (fullName.split(/[\s;,]/, 2).size() != 2) { return null }
        fullName.split(/[\s;,]/, 2)[1]
    }

}
