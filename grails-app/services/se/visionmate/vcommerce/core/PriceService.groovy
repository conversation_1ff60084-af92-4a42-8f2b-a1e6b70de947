package se.visionmate.vcommerce.core

import grails.gorm.transactions.Transactional
import grails.util.Environment
import groovy.time.TimeCategory
import org.hibernate.Session
import org.hibernate.SessionFactory
import se.visionmate.vcommerce.api.VismaConnectService
import se.visionmate.vcommerce.api.WooConnectService
import se.visionmate.vcommerce.enums.BatchUpdateType
import se.visionmate.vcommerce.enums.DiscountType
import se.visionmate.vcommerce.enums.PriceClassType
import se.visionmate.vcommerce.enums.PriceType
import se.visionmate.vcommerce.visma.CustomerPriceClass

class PriceService {

    SessionFactory sessionFactory
    AppService appService
    VismaConnectService vismaConnectService
    WooConnectService wooConnectService
    ProductService productService
    UpdateService updateService

    /**
     * Getting all prices from Visma and putting to the local DB
     */
    void populatePrices(Integer pageNumber = 1, Integer pageSize = 500) {
        Product product
        Customer customer
        Price price
        PriceClass priceClass
        PriceType priceType
        Map data = [:]

        def prices = vismaConnectService.getPrices([pageNumber:pageNumber, pageSize:pageSize])
        if (!prices) {
            println "No prices were found in Visma"
            return
        }

        prices.each { priceObj ->
            if (priceObj.expirationDate && isExpired(priceObj.expirationDate as String)) {
                println "Price \"${priceObj.recordId}:${priceObj.priceType}(${priceObj.inventoryId})\" is expired"
            } else {
                product = Product.findByNumber(priceObj.inventoryId as String)
                if (!product) {
                    println "Product \"${priceObj.inventoryId}(${priceObj.description})\" doesn't exist"
                } else {
                    priceType = getPriceTyp(priceObj.priceType as String)
                    data.type = priceType
                    data.recordId = priceObj.recordId as String
                    data.price = priceObj.price as String
                    data.promotion = priceObj.promotion as Boolean
                    data.ouM = priceObj.uoM as String
                    data.currency = priceObj.currency as String
                    data.expirationDate = priceObj.expirationDate ? getDateFromString(priceObj.expirationDate as String) : null
                    switch (priceType) {
                        case PriceType.CUSTOMER:
                            customer = Customer.findByNumber(priceObj.priceCode as String)
                            if (customer) {
                                price = Price.findByRecordId(priceObj.recordId as String) ?: createNewPrice(data, product)
                                if (price) {
                                    price.customer = customer
                                    price.save()
                                }
                            }
                            break
                        case PriceType.CUSTOMER_PRICE_CLASS:
                            priceClass = PriceClass.findByVismaId(priceObj.priceCode as String) ?:
                                    createNewPriceClass(PriceClassType.CUSTOMER, getPriceClassObj(priceObj.priceCode as String))
                            if (priceClass) {
                                price = Price.findByRecordId(priceObj.recordId as String) ?: createNewPrice(data, product)
                                if (price) {
                                    price.priceClass = priceClass
                                    price.save()
                                }
                            }
                            break
                        case PriceType.BASE:
                            price = Price.findByRecordId(priceObj.recordId as String) ?: createNewPrice(data, product)
                            break
                    }
                }
            }
        }
        Price.withSession { Session session ->
            session.flush()
            session.clear()
        }
        if (prices.size() > 0) {
            prices = []
            System.gc()
            populatePrices(++pageNumber)
        }
    }

    /**
     * Add a new price by "CustomerSalesPrice" object from Visma
     * @param priceVismaObj as Object
     * @return price as Price
     */
    @Transactional
    Price addNewPrice(def priceVismaObj) {
        Map data = [:]
        Price price

        if (priceVismaObj.expirationDate && isExpired(priceVismaObj.expirationDate as String)) {
            println "Price \"${priceVismaObj.recordId}:${priceVismaObj.priceType}(${priceVismaObj.inventoryId})\" is expired"
            return null
        } else {
            Product product = Product.findByNumber(priceVismaObj.inventoryId as String)
            if (!product) {
                println "Product \"${priceVismaObj.inventoryId}(${priceVismaObj.description})\" doesn't exist"
                return null
            } else {
                PriceType priceType = getPriceTyp(priceVismaObj.priceType as String)
                data.type = priceType
                data.recordId = priceVismaObj.recordId as String
                data.price = priceVismaObj.price as String
                data.promotion = priceVismaObj.promotion as Boolean
                data.ouM = priceVismaObj.uoM as String
                data.currency = priceVismaObj.currency as String
                data.expirationDate = priceVismaObj.expirationDate ? getDateFromString(priceVismaObj.expirationDate as String) : null
                switch (priceType) {
                    case PriceType.CUSTOMER:
                        Customer customer = Customer.findByNumber(priceVismaObj.priceCode as String)
                        if (customer) {
                            price = Price.findByRecordId(priceVismaObj.recordId as String) ?: createNewPrice(data, product)
                            if (price) {
                                price.customer = customer
                                price.save(flush: true)
                                return price
                            } else {
                                return null
                            }
                        } else {
                            return null
                        }
                        break
                    case PriceType.CUSTOMER_PRICE_CLASS:
                        PriceClass priceClass = PriceClass.findByVismaId(priceVismaObj.priceCode as String) ?:
                                createNewPriceClass(PriceClassType.CUSTOMER, getPriceClassObj(priceVismaObj.priceCode as String))
                        if (priceClass) {
                            price = Price.findByRecordId(priceVismaObj.recordId as String) ?: createNewPrice(data, product)
                            if (price) {
                                price.priceClass = priceClass
                                price.save(flush: true)
                                return price
                            } else {
                                return null
                            }
                        } else {
                            return null
                        }
                        break
                    case PriceType.BASE:
                        price = Price.findByRecordId(priceVismaObj.recordId as String) ?: createNewPrice(data, product)
                        return price ?: null
                        break
                    default:
                        return null
                }
            }
        }
    }

    /**
     * Creating a new price
     * @param data as Map
     * @param flush as Boolean
     * @return price as Price
     */
    @Transactional
    Price createNewPrice(Map data, Product product, Boolean flush = false) {
        Price price = new Price(
                type: data.type as PriceType,
                recordId: data.recordId,
                price: data.price,
                promotion: data.promotion,
                ouM: data.ouM,
                currency: data.currency,
                expirationDate: data.expirationDate as Date,
                product: product
        )
        if (price.validate()) {
            if (price.save(flush: flush)) {
                println "[${new Date().format('yyyy-MM-dd HH:mm:ss')}] $price - has been created"
                return price
            } else {
                println 'An error occurred while creating a new price'
                if (appService.debugMode) {
                    price.errors.allErrors.each { println it }
                }
                return null
            }
        } else {
            println 'Validation issue occurred while creating a new price'
            if (appService.debugMode) {
                price.errors.allErrors.each { println it }
            }
            return null
        }
    }

    /**
     * Creating a new priceClass
     * @param priceClassObj as Object
     * @param flush as Boolean
     * @return priceClass as PriceClass
     */
    @Transactional
    PriceClass createNewPriceClass(PriceClassType type, def priceClassObj, Boolean flush = false) {
        PriceClass priceClass = new PriceClass(
                vismaId: priceClassObj.id,
                description: priceClassObj.description ?: '',
                type: type
        )
        if (priceClass.validate()) {
            if (priceClass.save(flush: flush)) {
                println "[${new Date().format('yyyy-MM-dd HH:mm:ss')}] Price class $priceClass - has been created"
                return priceClass
            } else {
                println "An error occurred while creating a new priceClass '$priceClassObj.id - $priceClassObj.description'"
                if (appService.debugMode) {
                    priceClass.errors.allErrors.each { println it }
                }
                return null
            }
        } else {
            println "Validation issue occurred while creating new priceClass '$priceClassObj.id - $priceClassObj.description'"
            if (appService.debugMode) {
                priceClass.errors.allErrors.each { println it }
            }
            return null
        }
    }

    /**
     * Getting date format
     * @param date as String
     * @return dateFormat as String
     */
    String getDateFormat(String date) {
        switch (date.length()) {
            case 19:
                return "yyyy-MM-dd'T'HH:mm:ss"
                break
            case 22:
                return "yyyy-MM-dd'T'HH:mm:ss.SS"
                break
            case 23:
                return "yyyy-MM-dd'T'HH:mm:ss.SSS"
                break
            default:
                println "Date format not found for '$date'"
                return null
        }
    }

    /**
     * Getting date from String
     * @param date as String
     * @return date as date
     */
    Date getDateFromString(String date) {
        if (!date) return null
        return Date.parse(getDateFormat(date), date)
    }

    /**
     * Checking for expiration
     * @param expirationDate as String
     * @return expiration as Boolean
     */
    Boolean isExpired(String expirationDate) {
        if (!expirationDate) return false
        String dateFormat = getDateFormat(expirationDate)
        Date expDate = Date.parse(dateFormat, expirationDate)
        use(TimeCategory) {
            if (new Date() > expDate) {
                return true
            }
            return false
        }
    }

    /**
     * Checking for expiration
     * @param expirationDate as Date
     * @return expiration as Boolean
     */
    Boolean isExpired(Date expirationDate) {
        if (!expirationDate) return false
        use(TimeCategory) {
            if (new Date() > expirationDate) {
                return true
            }
            return false
        }
    }

    /**
     * Checking for effective date
     * @param effectiveDate: Date
     * @return
     */
    Boolean isEffective(Date effectiveDate) {
        if (!effectiveDate) return true
        use(TimeCategory) {
            if (new Date() >= effectiveDate) {
                return true
            }
            return false
        }
    }

    /**
     * Getting price type
     * @param priceType as String
     * @return priceType as PriceType
     */
    PriceType getPriceTyp(String priceType) {
        switch (priceType) {
            case 'Customer':
                return PriceType.CUSTOMER
                break
            case 'CustomerPriceClass':
                return PriceType.CUSTOMER_PRICE_CLASS
                break
            case 'Base':
                return PriceType.BASE
                break
            default:
                return PriceType.UNDEFINED
        }
    }

    /**
     * Getting price updates
     * @param lastModifiedDateTime: String
     * @param pageNumber: Integer
     * @param pageSize: Integer
     * @param prices: List<Price>
     */
    @Transactional
    void checkForPriceUpdates(String lastModifiedDateTime,
                              Integer pageNumber = 1,
                              Integer pageSize = 500,
                              List<Price> prices = []) {
        Map params = [:]
        if (lastModifiedDateTime) {
            params.lastModifiedDateTime = lastModifiedDateTime
            params.lastModifiedDateTimeCondition = '>'
            params.pageNumber = pageNumber
            params.pageSize = pageSize
        } else {
            params.pageNumber = pageNumber
            params.pageSize = pageSize
        }

        List<Object> pricesToCheck = vismaConnectService.getPrices(params) as List

        if (!pricesToCheck) {
            println 'There are no prices to check for updates'
            return
        }

        Price price
        List<Object> newVismaPrices = []
        List<Product> productsToUpdate = []
        List<Price> pricesToDelete = []
        if (!prices) {
            prices = Price.list()
        }

        pricesToCheck.each { priceToCheck ->
            if (priceToCheck.recordId) {
                price = prices.find {
                    it.recordId == priceToCheck.recordId as String
                }

                if (price) { prices.remove(price) }
                if (!price) {
                    newVismaPrices.push(priceToCheck)
                } else if (priceToCheck.expirationDate && isExpired(priceToCheck.expirationDate as String)) {
                    productsToUpdate.push(price.product)
                    try {
                        pricesToDelete.add(price)
                    } catch (Exception ex) {
                        println "[$appService.timeStamp] EXCEPTION: ${ex.message}"
                    }
                } else if (priceUpdated(price, priceToCheck)) {
                    productsToUpdate.push(price.product)
                } else {
                    println "price data for \"$price\" is relevant"
                }
            }
        }

        if (newVismaPrices) {
            newVismaPrices.each { newPriceVismaObj ->
                price = addNewPrice(newPriceVismaObj)
                if (price) {
                    productsToUpdate.push(price.product)
                }
            }
        }

        if (pricesToDelete) {
            Price.withNewSession { Session session ->
                pricesToDelete.each { Price priceToDelete ->
                    priceToDelete.delete()
                }
                session.flush()
                session.clear()
            }
        }

        if (productsToUpdate) {
            sessionFactory.currentSession.flush()
            productService.updateProductsInWoo(productsToUpdate.unique { it.id })
        }

        if (pricesToCheck.size() == pageSize) {
            sessionFactory.currentSession.flush()
            System.gc()
            checkForPriceUpdates(lastModifiedDateTime, ++pageNumber, pageSize, prices)
        } else if (!lastModifiedDateTime && prices.size() > 0) {
            sessionFactory.currentSession.flush()
            productsToUpdate = []
            Price.withNewSession { Session session ->
                prices.each {
                    productsToUpdate.add(it.product)
                    try {
                        it.delete()
                    } catch (Exception ex) {
                        println "[$appService.timeStamp] EXCEPTION: ${ex.message}"
                    }
                }
                session.flush()
                session.clear()
            }
            productService.updateProductsInWoo(productsToUpdate.unique { it.id })
        }
    }

    /**
     * Check a price for updates
     * @param price as Price
     * @param priceVismaObj as Object
     * @return Boolean
     */
    @Transactional
    Boolean priceUpdated(Price price, def priceVismaObj) {
        Boolean updated = false

        // Checking price fields
        updated = updateService.fieldUpdated(price, 'price', priceVismaObj.price) || updated
        updated = updateService.fieldUpdated(price, 'promotion', priceVismaObj.promotion) || updated
        updated = updateService.fieldUpdated(price, 'ouM', priceVismaObj.uoM) || updated
        updated = updateService.fieldUpdated(price, 'currency', priceVismaObj.currency) || updated
        updated
    }

    CustomerPriceClass getPriceClassObj (String priceCode) {
        return new CustomerPriceClass(priceCode)
    }

    @Transactional
    void checkForDiscountUpdates(String lastModifiedDateTime, Integer pageNumber = null, Integer pageSize = null) {
        Map params = [:]
        if (lastModifiedDateTime) {
            params.lastModifiedDateTime = lastModifiedDateTime
            params.lastModifiedDateTimeCondition = '>'
        } else if (pageNumber && pageSize) {
            params.pageNumber = pageNumber
            params.pageSize = pageSize
        }

        Object discountVismaObj = vismaConnectService.getDiscount(params)
        if (!discountVismaObj || !discountVismaObj.records) return

        List<Object> discountRecords = discountVismaObj.records as List
        discountVismaObj.remove('records')

        Discount discount
        PriceClass priceClass
        Customer customer
        Product product
        Set<Discount> discountsToUpdate = []
        Set<PriceClass> customerPriceClasses
        Set<PriceClass> productPriceClasses

        // processing only price classes
        discountRecords.findAll {it.customerPriceClasses || it.itemPriceClasses}.each { Object discountRecord ->
            discount = Discount.findByCodeAndSeries(discountRecord.discountCode as String, discountRecord.series as String) ?:
                    createNewDiscount([
                            'code': discountRecord.discountCode as String,
                            'series': discountRecord.series as String,
                            'description': discountRecord.description as String,
                            'active': discountRecord.active as Boolean,
                            'value': getDiscountValue(DiscountType.PERCENT, discountRecord.discountBreakpoints as List),
                            'type': DiscountType.PERCENT,
                            'effectiveDate': getDateFromString(discountRecord.effectiveDate as String),
                            'expirationDate': getDateFromString(discountRecord.expirationDate as String),
                    ])
            if (!discount) return

            if (!discountRecord.customerPriceClasses) {
                discountRecord.customerPriceClasses = []
            }

            if (!discountRecord.itemPriceClasses) {
                discountRecord.itemPriceClasses = []
            }

            Set<PriceClass> customerPriceClassesToRemoveFromDiscount = []
            Set<PriceClass> productPriceClassesToRemoveFromDiscount = []
            discountRecord.customerPriceClasses.each { Object customerPriceClassVismaObj ->
                priceClass = PriceClass.findByTypeAndVismaId(PriceClassType.CUSTOMER, customerPriceClassVismaObj.priceClassId as String)
                if (priceClass && (!discount.priceClasses || !discount.priceClasses?.contains(priceClass))) {
                    discount.addToPriceClasses(priceClass).save()
                    if (isEffective(discount.effectiveDate) && !isExpired(discount.expirationDate as Date)) {
                        discountsToUpdate.add(discount)
                    }
                }
            }

            customerPriceClasses = discount.priceClasses.findAll {it.type == PriceClassType.CUSTOMER}
            if (discountRecord.customerPriceClasses.size() != customerPriceClasses?.size()) {
                customerPriceClasses.each {PriceClass customerPriceClass ->
                    if (!discountRecord.customerPriceClasses.find {it.priceClassId == customerPriceClass.vismaId}) {
                        customerPriceClassesToRemoveFromDiscount.add(customerPriceClass)
                        discountsToUpdate.add(discount)
                    }
                }
            }

            discountRecord.itemPriceClasses.each { Object itemPriceClassVismaObj ->
                priceClass = PriceClass.findByTypeAndVismaId(PriceClassType.PRODUCT, itemPriceClassVismaObj.priceClassId as String)
                if (priceClass && (!discount.priceClasses || !discount.priceClasses?.contains(priceClass))) {
                    discount.addToPriceClasses(priceClass).save()
                    if (isEffective(discount.effectiveDate) && !isExpired(discount.expirationDate as Date)) {
                        discountsToUpdate.add(discount)
                    }
                }
            }

            productPriceClasses = discount.priceClasses.findAll {it.type == PriceClassType.PRODUCT}
            if (discountRecord.itemPriceClasses.size() != productPriceClasses?.size()) {
                productPriceClasses.each {PriceClass productPriceClass ->
                    if (!discountRecord.itemPriceClasses.find {it.priceClassId == productPriceClass.vismaId}) {
                        productPriceClassesToRemoveFromDiscount.add(productPriceClass)
                        discountsToUpdate.add(discount)
                    }
                }
            }

            if (customerPriceClassesToRemoveFromDiscount) {
                customerPriceClassesToRemoveFromDiscount.each { PriceClass customerPriceClassToRemoveFromDiscount ->
                    discount.removeFromPriceClasses(customerPriceClassToRemoveFromDiscount)
                    discount.save()
                }
            }

            if (productPriceClassesToRemoveFromDiscount) {
                productPriceClassesToRemoveFromDiscount.each { PriceClass productPriceClassToRemoveFromDiscount ->
                    discount.removeFromPriceClasses(productPriceClassToRemoveFromDiscount)
                    discount.save()
                }
            }

            if (discountUpdated(discount, discountRecord)) {
                discountsToUpdate.add(discount)
            }
        }

        // processing customer-item discounts
        discountRecords.findAll {it.customers || it.items}.each { Object discountRecord ->
            discount = Discount.findByCodeAndSeries(discountRecord.discountCode as String, discountRecord.series as String) ?:
                    createNewDiscount([
                            'code': discountRecord.discountCode as String,
                            'series': discountRecord.series as String,
                            'description': discountRecord.description as String,
                            'active': discountRecord.active as Boolean,
                            'value': getDiscountValue(DiscountType.PERCENT, discountRecord.discountBreakpoints as List),
                            'type': DiscountType.PERCENT,
                            'effectiveDate': getDateFromString(discountRecord.effectiveDate as String),
                            'expirationDate': getDateFromString(discountRecord.expirationDate as String),
                    ])
            if (!discount) return

            if (!discountRecord.customers) {
                discountRecord.customers = []
            }

            if (!discountRecord.items) {
                discountRecord.items = []
            }

            Set<Customer> customersToRemoveFromDiscount = []
            Set<Product> productsToRemoveFromDiscount = []
            discountRecord.customers.each { Object customerVismaObj ->
                customer = Customer.findByNumber(customerVismaObj.customer as String)
                if (customer && (!discount.customers || !discount.customers?.contains(customer))) {
                    discount.addToCustomers(customer).save()
                    if (isEffective(discount.effectiveDate) && !isExpired(discount.expirationDate as Date)) {
                        discountsToUpdate.add(discount)
                    }
                }
            }

            if (discountRecord.customers.size() != discount.customers?.size()) {
                discount.customers?.each {Customer discountCustomer ->
                    if (!discountRecord.customers.find {it.customer == discountCustomer.number}) {
                        customersToRemoveFromDiscount.add(discountCustomer)
                        discountsToUpdate.add(discount)
                    }
                }
            }

            discountRecord.items.each { Object itemVismaObj ->
                product = Product.findByNumber(itemVismaObj.itemId as String)
                if (product && (!discount.products || !discount.products?.contains(product))) {
                    discount.addToProducts(product).save()
                    if (isEffective(discount.effectiveDate) && !isExpired(discount.expirationDate as Date)) {
                        discountsToUpdate.add(discount)
                    }
                }
            }

            if (discountRecord.items.size() != discount.products?.size()) {
                discount.products?.each {Product discountProduct ->
                    if (!discountRecord.items.find {it.itemId == discountProduct.number}) {
                        productsToRemoveFromDiscount.add(discountProduct)
                        discountsToUpdate.add(discount)
                    }
                }
            }

            if (customersToRemoveFromDiscount) {
                customersToRemoveFromDiscount.each { Customer customerToRemoveFromDiscount ->
                    discount.removeFromCustomers(customerToRemoveFromDiscount)
                    discount.save()
                }
            }

            if (productsToRemoveFromDiscount) {
                productsToRemoveFromDiscount.each { Product productToRemoveFromDiscount ->
                    discount.removeFromProducts(productToRemoveFromDiscount)
                    discount.save()
                }
            }

            if (discountUpdated(discount, discountRecord)) {
                discountsToUpdate.add(discount)
            }
        }
//        sessionFactory.currentSession.flush()

        if (discountsToUpdate) {
            Set<Product> productsToUpdate = []
            discountsToUpdate.each {Discount discountToUpdate ->
                if (discountToUpdate.products) {
                    productsToUpdate.addAll(discountToUpdate.products)
                }
                discountToUpdate.priceClasses.findAll {
                    it.type == PriceClassType.PRODUCT
                }?.each {
                    productsToUpdate.addAll(it.products)
                }
            }
            productsToUpdate.collate(100).each {
                wooConnectService.batchUpdate(BatchUpdateType.PRODUCT, null, it, null)
            }
        }

        if (discountRecords.size() == discountVismaObj.pageSize) {
            pageNumber = discountVismaObj.pageNumber
            pageSize = discountVismaObj.pageSize
            checkForDiscountUpdates(lastModifiedDateTime, ++pageNumber, pageSize)
        }

    }

    /**
     * Create a new Discount\
     * @param data: Map
     * @param flush: Boolean
     * @return
     */
    @Transactional
    Discount createNewDiscount(Map<String, ?> data, Boolean flush = false) {
        Discount discount = new Discount(
                code: data.code,
                series: data.series,
                description: data.description,
                active: data.active,
                value: data.value as BigDecimal,
                type: data.type as DiscountType,
                effectiveDate: data.effectiveDate as Date,
                expirationDate: data.expirationDate as Date
        )
        if (discount.validate()) {
            if (discount.save(flush: flush)) {
                println "[$appService.timeStamp] new discount '$discount' - is created"
                return discount
            } else {
                println "An error occurred while creating new discount '$data.description'"
                if (appService.debugMode) {
                    discount.errors.allErrors.each { println it }
                }
                return null
            }
        } else {
            println "Validation issue occurred while creating new discount '$data.description'"
            if (appService.debugMode) {
                discount.errors.allErrors.each { println it }
            }
            return null
        }
    }

    /**
     * Get discount value
     * @param discountType: DiscountType
     * @param breakpoints: List<Object>
     * @return
     */
    BigDecimal getDiscountValue(DiscountType discountType, List<Object> breakpoints) {
        if (!breakpoints) return null
        List<Object> activeBreakpoints = breakpoints.findAll {it.active == true}
        if (!activeBreakpoints || activeBreakpoints.size() > 1) {
            println "No active breakpoint or more than 1 found"
            return null
        }
        switch (discountType) {
            case DiscountType.PERCENT:
                return activeBreakpoints[0]?.discountPercent as BigDecimal
            case DiscountType.AMOUNT:
                return activeBreakpoints[0]?.discountAmount as BigDecimal
        }
    }

    /**
     * Check discount fields for updates
     * @param discount: Discount
     * @param discountRecord: Object
     * @return Boolean
     */
    Boolean discountUpdated(Discount discount, Object discountRecord) {
        Boolean updated = false
        updated = updateService.fieldUpdated(discount, 'active', discountRecord.active) || updated
        updated = updateService.fieldUpdated(discount, 'value', getDiscountValue(DiscountType.PERCENT, discountRecord.discountBreakpoints as List)) || updated
        updated = updateService.fieldUpdated(discount, 'effectiveDate', getDateFromString(discountRecord.effectiveDate as String)) || updated
        updated = updateService.fieldUpdated(discount, 'expirationDate', getDateFromString(discountRecord.expirationDate as String)) || updated
        updated
    }

    /**
     * Get discount price for a price class
     * @param productPriceClass: PriceClass
     * @param customerPriceClassPrice: Price
     * @return String
     */
    String getPriceClassDiscountPrice(PriceClass productPriceClass, Price customerPriceClassPrice) {
        Discount discount = getPriceClassDiscount(productPriceClass, customerPriceClassPrice)
        if (discount) {
            BigDecimal price = customerPriceClassPrice.price as BigDecimal
            switch (discount.type) {
                case DiscountType.PERCENT:
                    return (price - (price * discount.value / 100)).setScale(2, BigDecimal.ROUND_HALF_EVEN) as String
                case DiscountType.AMOUNT:
                    return (price - discount.value).setScale(2, BigDecimal.ROUND_HALF_EVEN) as String
            }
        }
        customerPriceClassPrice.price
    }

    Discount getPriceClassDiscount(PriceClass productPriceClass, Price customerPriceClassPrice) {
        if (!productPriceClass || !customerPriceClassPrice) { return null }
        customerPriceClassPrice.priceClass?.discounts?.find {Discount priceClassDiscount ->
            priceClassDiscount.active && priceClassDiscount.priceClasses.find { PriceClass priceClass ->
                (priceClass.type == PriceClassType.PRODUCT) && (priceClass.id == productPriceClass.id)
            }
        }
    }

    /**
     * Get customer discount price for a product
     * @param customer: Customer
     * @param product: Product
     * @param price: String
     * @return String
     */
    String getCustomerDiscountPrice(Customer customer, Product product, String price) {
        Discount discount = getCustomerDiscount(customer, product)
        if (discount) {
            switch (discount.type) {
                case DiscountType.PERCENT:
                    return (new BigDecimal(price) - (new BigDecimal(price) * discount.value / 100)).setScale(2, BigDecimal.ROUND_HALF_EVEN) as String
                case DiscountType.AMOUNT:
                    return (new BigDecimal(price) - discount.value).setScale(2, BigDecimal.ROUND_HALF_EVEN) as String
            }
        }
        price
    }

    Discount getCustomerDiscount(Customer customer, Product product) {
        if (!customer || !product) { return null }
        customer.discounts?.find { Discount customerDiscount ->
            customerDiscount.active && customerDiscount.products.contains(product)
        }
    }

    /**
     * Get customer discount price
     * @param discount: Discount
     * @param price: String
     * @return String
     */
    String getCustomerDiscountPrice(Discount discount, String price) {
        if (discount && price) {
            switch (discount.type) {
                case DiscountType.PERCENT:
                    return (new BigDecimal(price) - (new BigDecimal(price) * discount.value / 100)).setScale(2, BigDecimal.ROUND_HALF_EVEN) as String
                case DiscountType.AMOUNT:
                    return (new BigDecimal(price) - discount.value).setScale(2, BigDecimal.ROUND_HALF_EVEN) as String
            }
        }
        price
    }

}