package se.visionmate.vcommerce.api

import grails.config.Config
import grails.core.support.GrailsConfigurationAware
import grails.util.Environment
import groovy.time.TimeCategory
import groovyx.net.http.ApacheHttpBuilder
import groovyx.net.http.HttpBuilder
import groovyx.net.http.optional.Download
import org.apache.http.client.config.RequestConfig
import org.apache.http.impl.client.HttpClientBuilder
import se.visionmate.vcommerce.core.AppService
import se.visionmate.vcommerce.core.ErrorHandlerService
import se.visionmate.vcommerce.dto.VismaAccessTokenDTO
import se.visionmate.vcommerce.enums.ApiService
import se.visionmate.vcommerce.enums.HttpMethod
import static groovyx.net.http.util.SslUtils.ignoreSslIssues

class HttpService implements GrailsConfigurationAware {
    private static Integer httpConnectTimeout, httpConnectionRequestTimeout
    private Date startDate

    AppService appService
    ErrorHandlerService errorHandlerService

    /**
     * Getting configuration
     * @param config
     */
    @Override
    void setConfiguration(Config config) {
        httpConnectTimeout = config.getProperty('appConfig.http.connectTimeout', Integer)
        httpConnectionRequestTimeout = config.getProperty('appConfig.http.connectionRequestTimeout', Integer)
    }

    /**
     * General method for calls http requests
     * @param apiService as Enum
     * @param httpMethod as Enum
     * @param target as String
     * @param requestConfig as Map
     * @param params as Map
     * @return
     */
    def doApiCall(ApiService apiService, HttpMethod httpMethod, String target, Map requestConfig, def params = [:]) {

        HttpBuilder http = ApacheHttpBuilder.configure {
            client.clientCustomizer { HttpClientBuilder builder ->
                RequestConfig.Builder requestBuilder = RequestConfig.custom()
                requestBuilder.connectTimeout = (httpConnectTimeout > 0) ? httpConnectTimeout * 1000 : 30000
                requestBuilder.connectionRequestTimeout = (httpConnectionRequestTimeout > 0) ? httpConnectionRequestTimeout * 1000 : 30000
                builder.defaultRequestConfig = requestBuilder.build()
            }
            // TODO: probably this should be removed
//            if (Environment.current == Environment.DEVELOPMENT) {
//                ignoreSslIssues execution
//            }
            request.contentType = 'application/json'
            request.charset = 'UTF-8'
            request.uri = requestConfig.requestUri
            request.uri.path = requestConfig.apiPath + requestConfig.apiVersion + "/$target"
            request.headers = getHeaders(apiService, requestConfig)
            switch (httpMethod) {
                case HttpMethod.GET:
                    request.uri.query = params
                    break
                case HttpMethod.POST:
                    request.body = params
                    break
                case HttpMethod.PUT:
                    request.body = params
                    break
                case HttpMethod.DELETE:
                    request.uri.query = params
                    break
            }
        }
        switch (httpMethod) {
            case HttpMethod.GET:
                // TODO: Must be improved for more flexibility
                if (target.startsWith('attachment')) {
                    startDate = new Date()
                    try {
                        http.get {
                            File file = Download.toTempFile(delegate)
                            if (appService.debugMode) {
                                println "HTTP-SUCCESS: FILE | target: '$target' downloaded in ${TimeCategory.minus(new Date(), startDate)}"
                            }
                            return file
                        }
                    } catch(Exception exception) {
                        println "\r\n[$appService.timeStamp] HTTP-ERROR: FILE | target: '$target'"
                        throw exception
                    }
                } else {
                    startDate = new Date()
                    http.get {
                        response.success { request, response ->
                            if (appService.debugMode) {
                                println "HTTP-SUCCESS: GET | target: '$target' received in ${TimeCategory.minus(new Date(), startDate)}"
                            }
                            return response
                        }
                        response.exception { Exception exception ->
                            println "\r\n[$appService.timeStamp] HTTP-ERROR: GET | target: '$target'"
                            throw exception
                        }
                    }
                }
                break
            case HttpMethod.POST:
                startDate = new Date()
                http.post {
                    response.success { request, response ->
                        if (appService.debugMode) {
                            println "HTTP-SUCCESS: POST | target: '$target' sent in ${TimeCategory.minus(new Date(), startDate)}"
                        }
                        return response
                    }
                    response.exception { Exception exception ->
                        println "\r\n[$appService.timeStamp] HTTP-ERROR: POST | target: '$target'"
                        throw exception
                    }
                }
                break
            case HttpMethod.PUT:
                startDate = new Date()
                http.put {
                    response.success { request, response ->
                        if (appService.debugMode) {
                            println "HTTP-SUCCESS: PUT | target: '$target' updated in ${TimeCategory.minus(new Date(), startDate)}"
                        }
                        return response
                    }
                    response.exception { Exception exception ->
                        println "\r\n[$appService.timeStamp] HTTP-ERROR:  PUT | target: '$target'"
                        throw exception
                    }
                }
                break
            case HttpMethod.DELETE:
                startDate = new Date()
                http.delete {
                    response.success { request, response ->
                        if (appService.debugMode) {
                            println "HTTP-SUCCESS: DELETE | target: '$target' deleted in ${TimeCategory.minus(new Date(), startDate)}"
                        }
                        return true
                    }
                    response.exception { Exception exception ->
                        println "\r\n[$appService.timeStamp] HTTP-ERROR: DELETE | target: '$target'"
                        throw exception
                    }
                }
                break
            default:
                println "${httpMethod.name()} method not presented"
                return null
        }
    }

    private static Map<String, String> getHeaders(ApiService apiService, Map<String, String> requestConfig) {
        switch (apiService) {
            case ApiService.VISMA:
                return ['Accept': 'application/json',
                        'Authorization': 'Bearer ' + requestConfig.auth]
                break
            case ApiService.WOO:
                return [
                        'Accept': 'application/json',
                        'Authorization': requestConfig.auth
                ]
                break
        }
    }

    def doVismaTokenCall(String tokenUri, String clientId, String clientSecret, String tenantId, String scope) {
        HttpBuilder http = ApacheHttpBuilder.configure {
            // TODO: probably should be removed
//            if (Environment.current == Environment.DEVELOPMENT) {
//                ignoreSslIssues execution
//            }
            request.contentType = 'application/x-www-form-urlencoded'
            request.charset = 'UTF-8'
            request.uri = tokenUri
            request.body = [
                    'grant_type': 'client_credentials',
                    'client_id': clientId,
                    'client_secret': clientSecret,
                    'tenant_id': tenantId,
                    'scope': scope,
            ] as Map
        }

        http.post {
            response.success { request, response ->
                if (appService.debugMode) {
                    println "HTTP-SUCCESS: POST | Visma Token: $response.access_token"
                }
                VismaAccessTokenDTO.fromMap(response as Map)
            }
            response.exception { Exception ex ->
                println "\r\n[$appService.timeStamp] HTTP-ERROR: POST | while getting token from Visma"
                throw ex
            }
        }
    }
}
