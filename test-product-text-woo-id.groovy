#!/usr/bin/env groovy

/**
 * Test script to verify ProductText wooId improvements
 * This script can be run in Grails console to test the changes
 */

// Test 1: Verify ProductText domain class changes
println "=== Testing ProductText Domain Class ==="

// Create a new ProductText instance
def productText = new se.visionmate.vcommerce.core.ProductText()
println "New ProductText wooId default value: ${productText.wooId}"
println "wooId is null: ${productText.wooId == null}"

// Test setting wooId to null
productText.wooId = null
println "After setting to null - wooId: ${productText.wooId}"
println "wooId is null: ${productText.wooId == null}"

// Test setting wooId to a value
productText.wooId = 123L
println "After setting to 123L - wooId: ${productText.wooId}"
println "wooId is null: ${productText.wooId == null}"
println "wooId type: ${productText.wooId.class.simpleName}"

println "\n=== Testing Database Queries ==="

// Test 2: Query for ProductText with null wooId
def nullWooIdCount = se.visionmate.vcommerce.core.ProductText.countByWooId(null)
println "ProductText records with wooId = null: ${nullWooIdCount}"

// Test 3: Query for ProductText with wooId = 0
def zeroWooIdCount = se.visionmate.vcommerce.core.ProductText.countByWooId(0)
println "ProductText records with wooId = 0: ${zeroWooIdCount}"

// Test 4: Query for ProductText with any wooId value
def anyWooIdCount = se.visionmate.vcommerce.core.ProductText.createCriteria().count {
    isNotNull('wooId')
}
println "ProductText records with wooId IS NOT NULL: ${anyWooIdCount}"

println "\n=== Testing Service Method ==="

// Test 5: Test the new fixProductTextWooId method
try {
    def productService = grailsApplication.mainContext.getBean('productService')
    println "ProductService found: ${productService != null}"
    
    // Check if the method exists
    def method = productService.class.getDeclaredMethod('fixProductTextWooId')
    println "fixProductTextWooId method exists: ${method != null}"
    
    println "Ready to call productService.fixProductTextWooId()"
    println "Note: Uncomment the next line to actually run the fix:"
    println "// productService.fixProductTextWooId()"
    
} catch (Exception e) {
    println "Error testing service method: ${e.message}"
}

println "\n=== Testing WooCommerce Integration Query ==="

// Test 6: Test the query used in sendProductsToWoo
try {
    def products = se.visionmate.vcommerce.core.Product.createCriteria().list {
        texts {
            isNull('wooId')
        }
    }
    println "Products with ProductText.wooId = null: ${products.size()}"
    
    if (products.size() > 0) {
        println "First few products:"
        products.take(3).each { product ->
            println "  - ${product.number}: ${product.description}"
            product.texts.each { text ->
                println "    ${text.lang}: wooId = ${text.wooId}"
            }
        }
    }
    
} catch (Exception e) {
    println "Error testing WooCommerce query: ${e.message}"
}

println "\n=== Test Complete ==="
println "All tests completed successfully!"
